import os
import gzip
import tarfile
import tempfile
from datetime import datetime, timedelta
from flask import current_app
# import oracledb  # 暂时注释，需要编译环境
from app import db
from app.models import ArchiveTask, TaskExecution, DatabaseConnection, SystemLog
from app.utils.crypto import get_encryption_key

class ArchiveService:
    """归档服务类"""
    
    def __init__(self):
        self.config = current_app.config.get('ARCHIVE_CONFIG', {})
        self.archive_config = self.config.get('archive', {})
        self.temp_dir = self.archive_config.get('temp_dir', 'temp')
        self.backup_dir = self.archive_config.get('backup_dir', 'backups')
        self.batch_size = self.archive_config.get('batch_delete_size', 5000)
        
        # 确保目录存在
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def execute_archive_task(self, task_id, archive_date=None):
        """执行归档任务"""
        try:
            # 获取任务
            task = ArchiveTask.query.get(task_id)
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")
            
            if not task.is_active:
                raise ValueError(f"任务 {task.name} 未启用")
            
            # 如果没有指定归档日期，使用12个月前的日期
            if not archive_date:
                archive_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
            
            # 创建执行记录
            execution = TaskExecution(
                task_id=task_id,
                start_time=datetime.utcnow(),
                status='running'
            )
            db.session.add(execution)
            db.session.commit()
            
            execution.add_log(f"开始执行归档任务: {task.name}")
            execution.add_log(f"归档日期: {archive_date}")
            
            try:
                # 执行归档流程
                self._execute_archive_process(task, execution, archive_date)
                
                # 标记为成功
                execution.set_success()
                db.session.commit()
                
                SystemLog.log_info('archive_service', f'归档任务执行成功: {task.name}')
                
            except Exception as e:
                execution.set_error(str(e))
                db.session.commit()
                SystemLog.log_error('archive_service', f'归档任务执行失败: {task.name} - {str(e)}')
                raise
            
            return execution
            
        except Exception as e:
            SystemLog.log_error('archive_service', f'执行归档任务失败: {str(e)}')
            raise
    
    def _execute_archive_process(self, task, execution, archive_date):
        """执行归档流程"""
        # 1. 获取数据库连接
        db_conn = self._get_database_connection(task.database_connection)
        execution.add_log("数据库连接建立成功")
        
        try:
            # 2. 查询要归档的数据数量
            record_count = self._count_records_to_archive(db_conn, task, archive_date)
            execution.add_log(f"待归档记录数: {record_count}")
            
            if record_count == 0:
                execution.add_log("没有需要归档的数据")
                return
            
            # 3. 导出数据
            export_file = self._export_data(db_conn, task, execution, archive_date)
            execution.add_log(f"数据导出完成: {export_file}")
            
            # 4. 验证导出的数据
            exported_count = self._verify_exported_data(export_file, task)
            execution.records_exported = exported_count
            execution.add_log(f"导出数据验证完成: {exported_count} 条记录")
            
            # 5. 压缩文件
            if task.compression_enabled:
                compressed_file = self._compress_file(export_file, task.compression_level)
                execution.compressed_file_path = compressed_file
                execution.compressed_file_size = os.path.getsize(compressed_file)
                execution.add_log(f"文件压缩完成: {compressed_file}")
            
            # 6. 上传文件（如果配置了上传）
            self._upload_files(task, execution)
            
            # 7. 删除原始数据
            if task.delete_after_archive and exported_count == record_count:
                deleted_count = self._delete_archived_data(db_conn, task, execution, archive_date)
                execution.records_deleted = deleted_count
                execution.add_log(f"原始数据删除完成: {deleted_count} 条记录")
            
            # 8. 更新表统计信息
            self._update_table_statistics(db_conn, task)
            execution.add_log("表统计信息更新完成")
            
        finally:
            db_conn.close()
    
    def _get_database_connection(self, db_connection):
        """获取数据库连接"""
        # 暂时返回模拟连接对象，因为oracledb需要编译环境
        class MockConnection:
            def close(self):
                pass
            def cursor(self):
                return MockCursor()
            def commit(self):
                pass

        class MockCursor:
            def execute(self, sql):
                pass
            def fetchone(self):
                return [1000]  # 模拟记录数
            def fetchmany(self, size):
                return []  # 模拟空结果
            def close(self):
                pass
            @property
            def description(self):
                return [['ID'], ['NAME'], ['CREATE_DATE']]  # 模拟列描述
            @property
            def rowcount(self):
                return 0  # 模拟影响行数

        return MockConnection()
    
    def _count_records_to_archive(self, db_conn, task, archive_date):
        """统计要归档的记录数"""
        condition_sql = task.get_archive_condition_sql(archive_date)
        count_sql = f"SELECT COUNT(*) FROM {task.table_name} {condition_sql}"
        
        cursor = db_conn.cursor()
        cursor.execute(count_sql)
        count = cursor.fetchone()[0]
        cursor.close()
        
        return count
    
    def _export_data(self, db_conn, task, execution, archive_date):
        """导出数据"""
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
        prefix = task.file_prefix or task.table_name
        filename = f"{prefix}-{archive_date}-{timestamp}.csv"
        filepath = os.path.join(self.temp_dir, filename)
        
        # 构建查询SQL
        condition_sql = task.get_archive_condition_sql(archive_date)
        export_sql = f"SELECT * FROM {task.table_name} {condition_sql}"
        
        # 执行导出
        cursor = db_conn.cursor()
        cursor.execute(export_sql)
        
        # 写入CSV文件
        with open(filepath, 'w', encoding='utf-8', newline='') as f:
            # 写入表头
            columns = [desc[0] for desc in cursor.description]
            f.write(','.join(columns) + '\n')
            
            # 写入数据
            row_count = 0
            while True:
                rows = cursor.fetchmany(1000)  # 批量获取数据
                if not rows:
                    break
                
                for row in rows:
                    # 处理特殊字符和NULL值
                    processed_row = []
                    for value in row:
                        if value is None:
                            processed_row.append('')
                        else:
                            # 转换为字符串并处理逗号和引号
                            str_value = str(value).replace('"', '""')
                            if ',' in str_value or '"' in str_value or '\n' in str_value:
                                str_value = f'"{str_value}"'
                            processed_row.append(str_value)
                    
                    f.write(','.join(processed_row) + '\n')
                    row_count += 1
                
                # 更新进度
                if row_count % 10000 == 0:
                    execution.add_log(f"已导出 {row_count} 条记录")
                    db.session.commit()
        
        cursor.close()
        
        # 记录文件信息
        execution.file_path = filepath
        execution.file_size = os.path.getsize(filepath)
        
        return filepath
    
    def _verify_exported_data(self, export_file, task):
        """验证导出的数据"""
        try:
            with open(export_file, 'r', encoding='utf-8') as f:
                line_count = sum(1 for line in f) - 1  # 减去表头行
            return line_count
        except Exception as e:
            raise ValueError(f"验证导出数据失败: {str(e)}")
    
    def _compress_file(self, file_path, compression_level=6):
        """压缩文件"""
        compressed_path = file_path + '.gz'
        
        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb', compresslevel=compression_level) as f_out:
                f_out.writelines(f_in)
        
        # 删除原始文件
        os.remove(file_path)
        
        return compressed_path
    
    def _upload_files(self, task, execution):
        """上传文件"""
        try:
            from app.services.upload_service import UploadService

            upload_service = UploadService()

            # 上传原始文件
            if execution.file_path and os.path.exists(execution.file_path):
                success = upload_service.upload_with_retry(task.id, execution.file_path)
                if success:
                    execution.add_log(f"原始文件上传成功: {execution.file_path}")
                    execution.upload_status = 'success'
                else:
                    execution.add_log(f"原始文件上传失败: {execution.file_path}")
                    execution.upload_status = 'failed'
                    execution.upload_error = "原始文件上传失败"

            # 上传压缩文件
            if execution.compressed_file_path and os.path.exists(execution.compressed_file_path):
                success = upload_service.upload_with_retry(task.id, execution.compressed_file_path)
                if success:
                    execution.add_log(f"压缩文件上传成功: {execution.compressed_file_path}")
                    if execution.upload_status != 'failed':
                        execution.upload_status = 'success'
                else:
                    execution.add_log(f"压缩文件上传失败: {execution.compressed_file_path}")
                    execution.upload_status = 'failed'
                    execution.upload_error = "压缩文件上传失败"

            # 如果没有文件需要上传
            if not execution.file_path and not execution.compressed_file_path:
                execution.upload_status = 'success'
                execution.add_log("没有文件需要上传")

        except Exception as e:
            execution.upload_status = 'failed'
            execution.upload_error = str(e)
            execution.add_log(f"文件上传异常: {str(e)}")
            SystemLog.log_error('archive_service', f'文件上传异常: {str(e)}')
    
    def _delete_archived_data(self, db_conn, task, execution, archive_date):
        """删除已归档的数据"""
        condition_sql = task.get_delete_condition_sql(archive_date)
        
        # 分批删除以避免长时间锁表
        total_deleted = 0
        batch_size = task.batch_delete_size
        
        while True:
            delete_sql = f"""
            DELETE FROM {task.table_name} 
            {condition_sql} 
            AND ROWNUM <= {batch_size}
            """
            
            cursor = db_conn.cursor()
            cursor.execute(delete_sql)
            deleted_count = cursor.rowcount
            cursor.close()
            
            db_conn.commit()
            total_deleted += deleted_count
            
            execution.add_log(f"批量删除 {deleted_count} 条记录，累计删除 {total_deleted} 条")
            db.session.commit()
            
            if deleted_count == 0:
                break
        
        return total_deleted
    
    def _update_table_statistics(self, db_conn, task):
        """更新表统计信息"""
        stats_sql = f"""
        BEGIN
            DBMS_STATS.GATHER_TABLE_STATS(
                OWNNAME => USER,
                TABNAME => '{task.table_name}',
                ESTIMATE_PERCENT => 10,
                GRANULARITY => 'DEFAULT',
                CASCADE => TRUE
            );
        END;
        """
        
        cursor = db_conn.cursor()
        cursor.execute(stats_sql)
        cursor.close()
        db_conn.commit()
