{% extends "base.html" %}

{% block title %}系统设置 - 数据库归档管理平台{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">系统设置</li>
{% endblock %}

{% block page_title %}系统设置{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧导航 -->
    <div class="col-md-3">
        <div class="card settings-nav">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>设置分类
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="tab"
                   data-bs-toggle="tooltip" title="配置系统基本参数和默认值">
                    <i class="fas fa-cog me-2"></i>基本设置
                    <small class="d-block text-muted">系统参数配置</small>
                </a>
                <a href="#compression" class="list-group-item list-group-item-action" data-bs-toggle="tab"
                   data-bs-toggle="tooltip" title="配置文件压缩相关设置">
                    <i class="fas fa-compress me-2"></i>压缩配置
                    <small class="d-block text-muted">文件压缩设置</small>
                </a>
                <a href="#upload" class="list-group-item list-group-item-action" data-bs-toggle="tab"
                   data-bs-toggle="tooltip" title="配置文件上传和命名规则">
                    <i class="fas fa-upload me-2"></i>上传配置
                    <small class="d-block text-muted">文件上传管理</small>
                </a>
                <a href="#notification" class="list-group-item list-group-item-action" data-bs-toggle="tab"
                   data-bs-toggle="tooltip" title="配置各种通知方式">
                    <i class="fas fa-bell me-2"></i>通知配置
                    <small class="d-block text-muted">消息通知设置</small>
                </a>
                <a href="#scheduler" class="list-group-item list-group-item-action" data-bs-toggle="tab"
                   data-bs-toggle="tooltip" title="管理任务调度器设置">
                    <i class="fas fa-clock me-2"></i>调度器设置
                    <small class="d-block text-muted">任务调度管理</small>
                </a>
                <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="tab"
                   data-bs-toggle="tooltip" title="配置安全策略和加密设置">
                    <i class="fas fa-shield-alt me-2"></i>安全设置
                    <small class="d-block text-muted">安全策略配置</small>
                </a>
                <a href="#logs" class="list-group-item list-group-item-action" data-bs-toggle="tab"
                   data-bs-toggle="tooltip" title="配置日志记录和管理">
                    <i class="fas fa-file-alt me-2"></i>日志设置
                    <small class="d-block text-muted">日志管理配置</small>
                </a>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportAllSettings()">
                        <i class="fas fa-download me-1"></i>导出配置
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="importSettings()">
                        <i class="fas fa-upload me-1"></i>导入配置
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="resetToDefaults()">
                        <i class="fas fa-undo me-1"></i>恢复默认
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="validateAllSettings()">
                        <i class="fas fa-check-circle me-1"></i>验证配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧内容 -->
    <div class="col-md-9">
        <div class="tab-content">
            <!-- 基本设置 -->
            <div class="tab-pane fade show active" id="general">
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>基本设置
                        </h5>
                        <small class="text-muted">配置系统的基本参数和默认值</small>
                    </div>
                    <div class="card-body">
                        <form id="generalForm" class="settings-form">
                            <!-- 应用信息 -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>应用信息
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="app_name" class="form-label">应用名称</label>
                                            <input type="text" class="form-control" id="app_name" name="app_name"
                                                   value="数据库归档管理平台" maxlength="50">
                                            <div class="form-text">显示在页面标题和通知中的应用名称</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="app_version" class="form-label">版本号</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="app_version" name="app_version"
                                                       value="v1.0.0" readonly>
                                                <span class="input-group-text">
                                                    <i class="fas fa-lock text-muted"></i>
                                                </span>
                                            </div>
                                            <div class="form-text">当前系统版本，自动管理</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 任务执行参数 -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-tasks me-2"></i>任务执行参数
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="default_batch_size" class="form-label">默认批处理大小</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="default_batch_size"
                                                       name="default_batch_size" value="1000" min="100" max="10000" step="100">
                                                <span class="input-group-text">条</span>
                                            </div>
                                            <div class="form-text">每批处理的记录数量，建议1000-5000</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="default_retention_days" class="form-label">默认保留天数</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="default_retention_days"
                                                       name="default_retention_days" value="365" min="1" max="3650">
                                                <span class="input-group-text">天</span>
                                            </div>
                                            <div class="form-text">数据保留的默认天数，超过此时间的数据将被归档</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_concurrent_tasks" class="form-label">最大并发任务数</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="max_concurrent_tasks"
                                                       name="max_concurrent_tasks" value="5" min="1" max="20">
                                                <span class="input-group-text">个</span>
                                            </div>
                                            <div class="form-text">同时执行的最大任务数量，建议不超过10</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="task_timeout" class="form-label">任务超时时间</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="task_timeout"
                                                       name="task_timeout" value="60" min="5" max="1440">
                                                <span class="input-group-text">分钟</span>
                                            </div>
                                            <div class="form-text">单个任务的最大执行时间，超时将被强制终止</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 系统行为 -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-cogs me-2"></i>系统行为
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="auto_cleanup"
                                                   name="auto_cleanup" checked>
                                            <label class="form-check-label" for="auto_cleanup">
                                                <strong>自动清理临时文件</strong>
                                                <div class="form-text">任务完成后自动删除临时文件以节省磁盘空间</div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="enable_monitoring"
                                                   name="enable_monitoring" checked>
                                            <label class="form-check-label" for="enable_monitoring">
                                                <strong>启用系统监控</strong>
                                                <div class="form-text">监控系统性能和资源使用情况</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="auto_backup"
                                                   name="auto_backup">
                                            <label class="form-check-label" for="auto_backup">
                                                <strong>自动备份配置</strong>
                                                <div class="form-text">定期备份系统配置和任务设置</div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="enable_api_rate_limit"
                                                   name="enable_api_rate_limit">
                                            <label class="form-check-label" for="enable_api_rate_limit">
                                                <strong>启用API限流</strong>
                                                <div class="form-text">限制API调用频率以保护系统</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-primary" onclick="saveGeneralSettings()">
                                    <i class="fas fa-save me-1"></i>保存设置
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetGeneralSettings()">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="previewGeneralSettings()">
                                    <i class="fas fa-eye me-1"></i>预览效果
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 压缩配置 -->
            <div class="tab-pane fade" id="compression">
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-compress me-2"></i>压缩配置
                        </h5>
                        <small class="text-muted">配置文件压缩相关设置，优化存储空间</small>
                    </div>
                    <div class="card-body">
                        <form id="compressionForm" class="settings-form">
                            <!-- 基本压缩设置 -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-toggle-on me-2"></i>基本设置
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="compression_enabled"
                                                   name="compression_enabled" checked>
                                            <label class="form-check-label" for="compression_enabled">
                                                <strong>启用自动压缩</strong>
                                                <div class="form-text">自动压缩导出的数据文件以节省存储空间</div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="compression_format" class="form-label">压缩格式</label>
                                            <select class="form-select" id="compression_format" name="compression_format" onchange="updateFormatDescription()">
                                                <option value="zip">ZIP (推荐)</option>
                                                <option value="gzip">GZIP</option>
                                                <option value="7z">7-Zip</option>
                                            </select>
                                            <div class="form-text" id="format-description">通用性好，支持密码保护</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 压缩参数 -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-sliders-h me-2"></i>压缩参数
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="compression_level" class="form-label">压缩级别</label>
                                            <select class="form-select" id="compression_level" name="compression_level" onchange="updateCompressionLevel()">
                                                <option value="1">最快压缩 (速度优先)</option>
                                                <option value="3">快速压缩 (平衡)</option>
                                                <option value="6" selected>标准压缩 (推荐)</option>
                                                <option value="9">最佳压缩 (体积优先)</option>
                                            </select>
                                            <div class="form-text" id="compression-level-info">
                                                <span class="badge bg-info me-1">速度: 中等</span>
                                                <span class="badge bg-success">压缩率: 好</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="min_file_size" class="form-label">最小压缩文件大小</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="min_file_size"
                                                       name="min_file_size" value="10" min="1" max="1000">
                                                <span class="input-group-text">MB</span>
                                            </div>
                                            <div class="form-text">小于此大小的文件不会被压缩，避免负优化</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 压缩预览 -->
                                <div class="config-preview">
                                    <div class="preview-label">压缩效果预览</div>
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <div class="preview-value">
                                                <strong>原始文件</strong><br>
                                                <span class="text-primary">100 MB</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="preview-value">
                                                <strong>压缩后</strong><br>
                                                <span class="text-success">~35 MB</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="preview-value">
                                                <strong>节省空间</strong><br>
                                                <span class="text-warning">~65%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 安全设置 -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-shield-alt me-2"></i>安全设置
                                </h6>
                                <div class="mb-3">
                                    <label for="compression_password" class="form-label">压缩密码</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="compression_password"
                                               name="compression_password" placeholder="留空表示不设置密码">
                                        <button class="btn btn-outline-secondary" type="button" onclick="generatePassword()">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">为压缩文件设置密码保护，增强数据安全性</div>
                                </div>
                            </div>

                            <!-- 高级选项 -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-cog me-2"></i>高级选项
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="delete_original"
                                                   name="delete_original">
                                            <label class="form-check-label" for="delete_original">
                                                <strong>压缩后删除原文件</strong>
                                                <div class="form-text text-warning">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    注意：原文件将被永久删除
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="verify_compression"
                                                   name="verify_compression" checked>
                                            <label class="form-check-label" for="verify_compression">
                                                <strong>验证文件完整性</strong>
                                                <div class="form-text">压缩后验证文件是否完整，确保数据安全</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="split_large_files"
                                                   name="split_large_files">
                                            <label class="form-check-label" for="split_large_files">
                                                <strong>分割大文件</strong>
                                                <div class="form-text">将大文件分割为多个小文件便于传输</div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="split_size" class="form-label">分割大小</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="split_size"
                                                       name="split_size" value="100" min="10" max="1000">
                                                <span class="input-group-text">MB</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-primary" onclick="saveCompressionSettings()">
                                    <i class="fas fa-save me-1"></i>保存设置
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="testCompression()">
                                    <i class="fas fa-vial me-1"></i>测试压缩
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetCompressionSettings()">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="showCompressionHelp()">
                                    <i class="fas fa-question-circle me-1"></i>帮助
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 上传配置 -->
            <div class="tab-pane fade" id="upload">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>上传配置
                        </h5>
                        <button class="btn btn-primary btn-sm" onclick="showUploadConfigDialog()">
                            <i class="fas fa-plus me-1"></i>添加配置
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>配置名称</th>
                                        <th>上传类型</th>
                                        <th>目标地址</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="upload-configs-table">
                                    <tr>
                                        <td colspan="5" class="text-center py-3">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 文件命名规则 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tag me-2"></i>文件命名规则
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="namingForm">
                            <div class="mb-3">
                                <label for="file_name_template" class="form-label">文件名模板</label>
                                <input type="text" class="form-control" id="file_name_template" name="file_name_template"
                                       value="{table_name}_{date}_{time}">
                                <div class="form-text">
                                    可用变量: {table_name}, {task_name}, {date}, {time}, {timestamp}, {execution_id}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date_format" class="form-label">日期格式</label>
                                        <select class="form-select" id="date_format" name="date_format">
                                            <option value="YYYYMMDD">YYYYMMDD</option>
                                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                            <option value="YYYY_MM_DD">YYYY_MM_DD</option>
                                            <option value="DDMMYYYY">DDMMYYYY</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="time_format" class="form-label">时间格式</label>
                                        <select class="form-select" id="time_format" name="time_format">
                                            <option value="HHMMSS">HHMMSS</option>
                                            <option value="HH-MM-SS">HH-MM-SS</option>
                                            <option value="HH_MM_SS">HH_MM_SS</option>
                                            <option value="HHMM">HHMM</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">预览示例</label>
                                <div class="alert alert-info">
                                    <strong>原始文件:</strong> <span id="original-filename">user_data_20241201_143022.csv</span><br>
                                    <strong>压缩文件:</strong> <span id="compressed-filename">user_data_20241201_143022.zip</span>
                                </div>
                            </div>

                            <button type="button" class="btn btn-primary" onclick="saveNamingSettings()">
                                <i class="fas fa-save me-1"></i>保存设置
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="previewFilename()">
                                <i class="fas fa-eye me-1"></i>预览文件名
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 通知配置 -->
            <div class="tab-pane fade" id="notification">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>通知配置
                        </h5>
                        <button class="btn btn-primary btn-sm" onclick="showNotificationConfigDialog()">
                            <i class="fas fa-plus me-1"></i>添加通知
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>配置名称</th>
                                        <th>通知类型</th>
                                        <th>触发条件</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="notification-configs-table">
                                    <tr>
                                        <td colspan="5" class="text-center py-3">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 调度器设置 -->
            <div class="tab-pane fade" id="scheduler">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>调度器设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5>调度器状态</h5>
                                        <span class="status-badge status-success" id="scheduler-status">
                                            <i class="fas fa-check-circle"></i>运行中
                                        </span>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-outline-warning" onclick="stopScheduler()">停止</button>
                                            <button class="btn btn-sm btn-outline-success" onclick="startScheduler()">启动</button>
                                            <button class="btn btn-sm btn-outline-info" onclick="restartScheduler()">重启</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5>活跃任务数</h5>
                                        <h3 class="text-primary" id="active-jobs-count">0</h3>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-outline-secondary" onclick="refreshSchedulerStatus()">刷新状态</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form id="schedulerForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="max_workers" class="form-label">最大工作线程数</label>
                                        <input type="number" class="form-control" id="max_workers" name="max_workers" value="10">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="job_defaults_coalesce" class="form-label">任务合并</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="job_defaults_coalesce" name="job_defaults_coalesce" checked>
                                            <label class="form-check-label" for="job_defaults_coalesce">
                                                启用任务合并
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="misfire_grace_time" class="form-label">错过执行容忍时间(秒)</label>
                                        <input type="number" class="form-control" id="misfire_grace_time" name="misfire_grace_time" value="300">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="max_instances" class="form-label">最大实例数</label>
                                        <input type="number" class="form-control" id="max_instances" name="max_instances" value="1">
                                    </div>
                                </div>
                            </div>

                            <button type="button" class="btn btn-primary" onclick="saveSchedulerSettings()">
                                <i class="fas fa-save me-1"></i>保存设置
                            </button>
                        </form>

                        <!-- 当前任务列表 -->
                        <div class="mt-4">
                            <h6>当前调度任务</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>任务ID</th>
                                            <th>任务名称</th>
                                            <th>下次执行时间</th>
                                            <th>触发器</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scheduler-jobs-table">
                                        <tr>
                                            <td colspan="5" class="text-center py-2">
                                                <small class="text-muted">正在加载...</small>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 安全设置 -->
            <div class="tab-pane fade" id="security">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>安全设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="securityForm">
                            <div class="mb-4">
                                <h6>密码策略</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="min_password_length" class="form-label">最小密码长度</label>
                                            <input type="number" class="form-control" id="min_password_length" name="min_password_length" value="8">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password_expiry_days" class="form-label">密码过期天数</label>
                                            <input type="number" class="form-control" id="password_expiry_days" name="password_expiry_days" value="90">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="require_special_chars" name="require_special_chars">
                                        <label class="form-check-label" for="require_special_chars">
                                            要求包含特殊字符
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h6>访问控制</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="session_timeout" class="form-label">会话超时时间(分钟)</label>
                                            <input type="number" class="form-control" id="session_timeout" name="session_timeout" value="30">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_login_attempts" class="form-label">最大登录尝试次数</label>
                                            <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" value="5">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="allowed_ips" class="form-label">允许的IP地址</label>
                                    <textarea class="form-control" id="allowed_ips" name="allowed_ips" rows="3"
                                              placeholder="每行一个IP地址或IP段，例如：***********/24"></textarea>
                                    <div class="form-text">留空表示允许所有IP访问</div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h6>数据加密</h6>
                                <div class="mb-3">
                                    <label for="encryption_algorithm" class="form-label">加密算法</label>
                                    <select class="form-select" id="encryption_algorithm" name="encryption_algorithm">
                                        <option value="AES-256">AES-256</option>
                                        <option value="AES-128">AES-128</option>
                                        <option value="ChaCha20">ChaCha20</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="encrypt_database_passwords" name="encrypt_database_passwords" checked>
                                        <label class="form-check-label" for="encrypt_database_passwords">
                                            加密存储数据库密码
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="encrypt_export_files" name="encrypt_export_files">
                                        <label class="form-check-label" for="encrypt_export_files">
                                            加密导出文件
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">
                                <i class="fas fa-save me-1"></i>保存设置
                            </button>
                            <button type="button" class="btn btn-warning" onclick="regenerateEncryptionKey()">
                                <i class="fas fa-key me-1"></i>重新生成加密密钥
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 日志设置 -->
            <div class="tab-pane fade" id="logs">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>日志设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="logsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_level" class="form-label">日志级别</label>
                                        <select class="form-select" id="log_level" name="log_level">
                                            <option value="DEBUG">DEBUG</option>
                                            <option value="INFO" selected>INFO</option>
                                            <option value="WARNING">WARNING</option>
                                            <option value="ERROR">ERROR</option>
                                            <option value="CRITICAL">CRITICAL</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_retention_days" class="form-label">日志保留天数</label>
                                        <input type="number" class="form-control" id="log_retention_days" name="log_retention_days" value="30">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="max_log_file_size" class="form-label">单个日志文件最大大小(MB)</label>
                                        <input type="number" class="form-control" id="max_log_file_size" name="max_log_file_size" value="10">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_backup_count" class="form-label">日志备份文件数量</label>
                                        <input type="number" class="form-control" id="log_backup_count" name="log_backup_count" value="5">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable_sql_logging" name="enable_sql_logging">
                                    <label class="form-check-label" for="enable_sql_logging">
                                        启用SQL查询日志
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable_performance_logging" name="enable_performance_logging" checked>
                                    <label class="form-check-label" for="enable_performance_logging">
                                        启用性能日志
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable_audit_logging" name="enable_audit_logging" checked>
                                    <label class="form-check-label" for="enable_audit_logging">
                                        启用审计日志
                                    </label>
                                </div>
                            </div>

                            <button type="button" class="btn btn-primary" onclick="saveLogsSettings()">
                                <i class="fas fa-save me-1"></i>保存设置
                            </button>
                            <button type="button" class="btn btn-info" onclick="downloadLogs()">
                                <i class="fas fa-download me-1"></i>下载日志
                            </button>
                            <button type="button" class="btn btn-warning" onclick="clearLogs()">
                                <i class="fas fa-trash me-1"></i>清空日志
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传配置模态框 -->
<div class="modal fade" id="uploadConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadConfigModalTitle">
                    <i class="fas fa-plus me-2"></i>添加上传配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadConfigForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="upload_name" class="form-label">配置名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="upload_name" name="upload_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="upload_type" class="form-label">上传类型</label>
                                <select class="form-select" id="upload_type" name="upload_type" onchange="toggleUploadFields()">
                                    <option value="sftp">SFTP</option>
                                    <option value="ftp">FTP</option>
                                    <option value="local">本地目录</option>
                                    <option value="s3">Amazon S3</option>
                                    <option value="azure">Azure Blob</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="remote-fields">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="upload_host" class="form-label">主机地址</label>
                                    <input type="text" class="form-control" id="upload_host" name="upload_host">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="upload_port" class="form-label">端口号</label>
                                    <input type="number" class="form-control" id="upload_port" name="upload_port" value="22">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="upload_username" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="upload_username" name="upload_username">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="upload_password" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="upload_password" name="upload_password">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="upload_remote_path" class="form-label">远程路径/本地路径</label>
                        <input type="text" class="form-control" id="upload_remote_path" name="upload_remote_path"
                               placeholder="/data/archive/">
                    </div>

                    <div class="ftp-fields" style="display: none;">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="upload_passive_mode" name="upload_passive_mode" checked>
                                <label class="form-check-label" for="upload_passive_mode">
                                    使用被动模式
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="upload_is_default" name="upload_is_default">
                            <label class="form-check-label" for="upload_is_default">
                                设为默认上传配置
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="upload_is_active" name="upload_is_active" checked>
                            <label class="form-check-label" for="upload_is_active">
                                启用此配置
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" onclick="testUploadConfig()">
                    <i class="fas fa-check"></i> 测试连接
                </button>
                <button type="button" class="btn btn-primary" onclick="saveUploadConfig()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 通知配置模态框 -->
<div class="modal fade" id="notificationConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationConfigModalTitle">
                    <i class="fas fa-plus me-2"></i>添加通知配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="notificationConfigForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notification_name" class="form-label">配置名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="notification_name" name="notification_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notification_type" class="form-label">通知类型</label>
                                <select class="form-select" id="notification_type" name="notification_type" onchange="toggleNotificationFields()">
                                    <option value="email">邮件</option>
                                    <option value="wechat">企业微信</option>
                                    <option value="dingtalk">钉钉</option>
                                    <option value="slack">Slack</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 邮件配置 -->
                    <div class="email-fields">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="smtp_server" class="form-label">SMTP服务器</label>
                                    <input type="text" class="form-control" id="smtp_server" name="smtp_server">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="smtp_port" class="form-label">端口号</label>
                                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="587">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_username" class="form-label">邮箱用户名</label>
                                    <input type="email" class="form-control" id="email_username" name="email_username">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_password" class="form-label">邮箱密码</label>
                                    <input type="password" class="form-control" id="email_password" name="email_password">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email_recipients" class="form-label">收件人</label>
                            <textarea class="form-control" id="email_recipients" name="email_recipients" rows="2"
                                      placeholder="多个邮箱地址用逗号分隔"></textarea>
                        </div>
                    </div>

                    <!-- 企业微信配置 -->
                    <div class="wechat-fields" style="display: none;">
                        <div class="mb-3">
                            <label for="wechat_webhook" class="form-label">Webhook URL</label>
                            <input type="url" class="form-control" id="wechat_webhook" name="wechat_webhook">
                        </div>

                        <div class="mb-3">
                            <label for="wechat_mentioned" class="form-label">@用户列表</label>
                            <input type="text" class="form-control" id="wechat_mentioned" name="wechat_mentioned"
                                   placeholder="用户ID，多个用逗号分隔">
                        </div>
                    </div>

                    <!-- 钉钉配置 -->
                    <div class="dingtalk-fields" style="display: none;">
                        <div class="mb-3">
                            <label for="dingtalk_webhook" class="form-label">Webhook URL</label>
                            <input type="url" class="form-control" id="dingtalk_webhook" name="dingtalk_webhook">
                        </div>

                        <div class="mb-3">
                            <label for="dingtalk_secret" class="form-label">加签密钥</label>
                            <input type="text" class="form-control" id="dingtalk_secret" name="dingtalk_secret">
                        </div>

                        <div class="mb-3">
                            <label for="dingtalk_at_mobiles" class="form-label">@手机号</label>
                            <input type="text" class="form-control" id="dingtalk_at_mobiles" name="dingtalk_at_mobiles"
                                   placeholder="手机号，多个用逗号分隔">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="notify_on_success" name="notify_on_success" checked>
                                    <label class="form-check-label" for="notify_on_success">
                                        成功时通知
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="notify_on_failure" name="notify_on_failure" checked>
                                    <label class="form-check-label" for="notify_on_failure">
                                        失败时通知
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="notify_on_warning" name="notify_on_warning">
                                    <label class="form-check-label" for="notify_on_warning">
                                        警告时通知
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notification_is_active" name="notification_is_active" checked>
                            <label class="form-check-label" for="notification_is_active">
                                启用此配置
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" onclick="testNotificationConfig()">
                    <i class="fas fa-paper-plane"></i> 发送测试
                </button>
                <button type="button" class="btn btn-primary" onclick="saveNotificationConfig()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script>
// 全局变量
let currentUploadConfig = null;
let currentNotificationConfig = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadUploadConfigs();
    loadNotificationConfigs();
    loadSchedulerStatus();
    loadSystemSettings();

    // 初始化文件名预览
    previewFilename();

    // 绑定表单变化事件
    const templateField = document.getElementById('file_name_template');
    const dateFormatField = document.getElementById('date_format');
    const timeFormatField = document.getElementById('time_format');

    if (templateField) templateField.addEventListener('input', previewFilename);
    if (dateFormatField) dateFormatField.addEventListener('change', previewFilename);
    if (timeFormatField) timeFormatField.addEventListener('change', previewFilename);

    // 初始化压缩设置
    updateFormatDescription();
    updateCompressionLevel();
});

// 保存基本设置
async function saveGeneralSettings() {
    const form = document.getElementById('generalForm');
    const formData = new FormData(form);

    const data = {
        app_name: formData.get('app_name'),
        default_batch_size: parseInt(formData.get('default_batch_size')),
        default_retention_days: parseInt(formData.get('default_retention_days')),
        max_concurrent_tasks: parseInt(formData.get('max_concurrent_tasks')),
        task_timeout: parseInt(formData.get('task_timeout')),
        auto_cleanup: formData.has('auto_cleanup'),
        enable_monitoring: formData.has('enable_monitoring')
    };

    try {
        showLoading();
        const response = await axios.post('/api/system/settings/general', data);

        if (response.data.success) {
            showMessage('基本设置保存成功', 'success');
        } else {
            showMessage('保存失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存基本设置失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 保存压缩设置
async function saveCompressionSettings() {
    const form = document.getElementById('compressionForm');
    const formData = new FormData(form);

    const data = {
        compression_enabled: formData.has('compression_enabled'),
        compression_format: formData.get('compression_format'),
        compression_level: parseInt(formData.get('compression_level')),
        min_file_size: parseInt(formData.get('min_file_size')),
        compression_password: formData.get('compression_password'),
        delete_original: formData.has('delete_original'),
        verify_compression: formData.has('verify_compression')
    };

    try {
        showLoading();
        const response = await axios.post('/api/system/settings/compression', data);

        if (response.data.success) {
            showMessage('压缩设置保存成功', 'success');
        } else {
            showMessage('保存失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存压缩设置失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 测试压缩
async function testCompression() {
    try {
        showLoading();
        const response = await axios.post('/api/system/test-compression');

        if (response.data.success) {
            showMessage('压缩测试成功: ' + response.data.message, 'success');
        } else {
            showMessage('压缩测试失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('压缩测试失败:', error);
        showMessage('压缩测试失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 显示上传配置对话框
function showUploadConfigDialog(config = null) {
    const modal = new bootstrap.Modal(document.getElementById('uploadConfigModal'));
    const form = document.getElementById('uploadConfigForm');
    const title = document.getElementById('uploadConfigModalTitle');

    if (config) {
        // 编辑模式
        title.innerHTML = '<i class="fas fa-edit me-2"></i>编辑上传配置';
        currentUploadConfig = config;

        // 填充表单数据
        form.upload_name.value = config.name || '';
        form.upload_type.value = config.upload_type || 'sftp';
        form.upload_host.value = config.host || '';
        form.upload_port.value = config.port || 22;
        form.upload_username.value = config.username || '';
        form.upload_password.value = '';
        form.upload_remote_path.value = config.remote_path || '';
        form.upload_passive_mode.checked = config.passive_mode || false;
        form.upload_is_default.checked = config.is_default || false;
        form.upload_is_active.checked = config.is_active !== false;
    } else {
        // 新增模式
        title.innerHTML = '<i class="fas fa-plus me-2"></i>添加上传配置';
        currentUploadConfig = null;
        form.reset();
        form.upload_type.value = 'sftp';
        form.upload_port.value = 22;
        form.upload_is_active.checked = true;
    }

    toggleUploadFields();
    modal.show();
}

// 切换上传字段显示
function toggleUploadFields() {
    const uploadType = document.getElementById('upload_type').value;
    const remoteFields = document.querySelector('.remote-fields');
    const ftpFields = document.querySelector('.ftp-fields');

    if (uploadType === 'local') {
        if (remoteFields) remoteFields.style.display = 'none';
        if (ftpFields) ftpFields.style.display = 'none';
        const portField = document.getElementById('upload_port');
        if (portField) portField.value = '';
    } else {
        if (remoteFields) remoteFields.style.display = 'block';

        if (uploadType === 'ftp') {
            if (ftpFields) ftpFields.style.display = 'block';
            const portField = document.getElementById('upload_port');
            if (portField) portField.value = 21;
        } else if (uploadType === 'sftp') {
            if (ftpFields) ftpFields.style.display = 'none';
            const portField = document.getElementById('upload_port');
            if (portField) portField.value = 22;
        } else {
            if (ftpFields) ftpFields.style.display = 'none';
            const portField = document.getElementById('upload_port');
            if (portField) portField.value = '';
        }
    }
}

// 保存上传配置
async function saveUploadConfig() {
    const form = document.getElementById('uploadConfigForm');

    if (!validateForm(form)) {
        showMessage('请填写所有必填字段', 'error');
        return;
    }

    const formData = new FormData(form);
    const data = {
        name: formData.get('upload_name'),
        upload_type: formData.get('upload_type'),
        host: formData.get('upload_host'),
        port: parseInt(formData.get('upload_port')) || null,
        username: formData.get('upload_username'),
        password: formData.get('upload_password'),
        remote_path: formData.get('upload_remote_path'),
        passive_mode: formData.has('upload_passive_mode'),
        is_default: formData.has('upload_is_default'),
        is_active: formData.has('upload_is_active')
    };

    try {
        showLoading();
        let response;

        if (currentUploadConfig) {
            response = await axios.put(`/api/upload-configs/${currentUploadConfig.id}`, data);
        } else {
            response = await axios.post('/api/upload-configs', data);
        }

        if (response.data.success) {
            showMessage(response.data.message, 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('uploadConfigModal'));
            modal.hide();

            loadUploadConfigs();
        } else {
            showMessage(response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存上传配置失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 测试上传配置
async function testUploadConfig() {
    const form = document.getElementById('uploadConfigForm');

    if (!validateForm(form)) {
        showMessage('请填写连接信息', 'error');
        return;
    }

    try {
        showLoading();

        if (currentUploadConfig) {
            const response = await axios.post(`/api/upload-configs/${currentUploadConfig.id}/test`);

            if (response.data.success) {
                showMessage('连接测试成功: ' + response.data.message, 'success');
            } else {
                showMessage('连接测试失败: ' + response.data.message, 'error');
            }
        } else {
            showMessage('请先保存配置后再测试', 'warning');
        }
    } catch (error) {
        console.error('测试上传配置失败:', error);
        showMessage('测试失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 加载上传配置列表
async function loadUploadConfigs() {
    try {
        const response = await axios.get('/api/upload-configs');
        const configs = response.data.data;

        renderUploadConfigsTable(configs);
    } catch (error) {
        console.error('加载上传配置失败:', error);
        showMessage('加载上传配置失败: ' + error.message, 'error');
    }
}

// 渲染上传配置表格
function renderUploadConfigsTable(configs) {
    const tbody = document.getElementById('upload-configs-table');

    if (configs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-3">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无上传配置</div>
                    <button class="btn btn-primary btn-sm mt-2" onclick="showUploadConfigDialog()">
                        <i class="fas fa-plus me-1"></i>添加第一个配置
                    </button>
                </td>
            </tr>
        `;
        return;
    }

    const html = configs.map(config => `
        <tr>
            <td>
                <div class="fw-bold">${config.name}</div>
                ${config.is_default ? '<small class="badge bg-primary">默认</small>' : ''}
            </td>
            <td>
                <span class="badge bg-info">${config.upload_type.toUpperCase()}</span>
            </td>
            <td>
                ${config.upload_type === 'local' ?
                    config.local_path || config.remote_path :
                    `${config.host}:${config.port || ''}`
                }
            </td>
            <td>
                <span class="status-badge status-${config.is_active ? 'success' : 'error'}">
                    <i class="fas fa-${config.is_active ? 'check' : 'times'}-circle"></i>
                    ${config.is_active ? '启用' : '禁用'}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-success" onclick="testUploadConfigById(${config.id})" title="测试连接">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-outline-primary" onclick="editUploadConfig(${config.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteUploadConfig(${config.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = html;
}

// 编辑上传配置
async function editUploadConfig(id) {
    try {
        const response = await axios.get(`/api/upload-configs/${id}`);
        const config = response.data.data;

        showUploadConfigDialog(config);
    } catch (error) {
        console.error('获取上传配置失败:', error);
        showMessage('获取配置信息失败: ' + error.message, 'error');
    }
}

// 删除上传配置
async function deleteUploadConfig(id) {
    const confirmed = await confirmDialog('确定要删除这个上传配置吗？此操作不可撤销。');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.delete(`/api/upload-configs/${id}`);

        if (response.data.success) {
            showMessage(response.data.message, 'success');
            loadUploadConfigs();
        } else {
            showMessage(response.data.message, 'error');
        }
    } catch (error) {
        console.error('删除上传配置失败:', error);
        showMessage('删除失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 通过ID测试上传配置
async function testUploadConfigById(id) {
    try {
        showLoading();
        const response = await axios.post(`/api/upload-configs/${id}/test`);

        if (response.data.success) {
            showMessage('连接测试成功: ' + response.data.message, 'success');
        } else {
            showMessage('连接测试失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('测试上传配置失败:', error);
        showMessage('测试失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 保存文件命名设置
async function saveNamingSettings() {
    const form = document.getElementById('namingForm');
    const formData = new FormData(form);

    const data = {
        file_name_template: formData.get('file_name_template'),
        date_format: formData.get('date_format'),
        time_format: formData.get('time_format')
    };

    try {
        showLoading();
        const response = await axios.post('/api/system/settings/naming', data);

        if (response.data.success) {
            showMessage('文件命名设置保存成功', 'success');
            previewFilename();
        } else {
            showMessage('保存失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存文件命名设置失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 预览文件名
function previewFilename() {
    const templateField = document.getElementById('file_name_template');
    const dateFormatField = document.getElementById('date_format');
    const timeFormatField = document.getElementById('time_format');

    if (!templateField || !dateFormatField || !timeFormatField) {
        return; // 如果字段不存在，直接返回
    }

    const template = templateField.value;
    const dateFormat = dateFormatField.value;
    const timeFormat = timeFormatField.value;

    // 模拟数据
    const now = new Date();
    const variables = {
        table_name: 'user_data',
        task_name: 'daily_archive',
        date: formatDateByPattern(now, dateFormat),
        time: formatTimeByPattern(now, timeFormat),
        timestamp: Math.floor(now.getTime() / 1000),
        execution_id: '12345'
    };

    let originalFilename = template;
    let compressedFilename = template;

    // 替换变量
    Object.keys(variables).forEach(key => {
        const regex = new RegExp(`{${key}}`, 'g');
        originalFilename = originalFilename.replace(regex, variables[key]);
        compressedFilename = compressedFilename.replace(regex, variables[key]);
    });

    // 添加扩展名
    originalFilename += '.csv';
    compressedFilename += '.zip';

    const originalElement = document.getElementById('original-filename');
    const compressedElement = document.getElementById('compressed-filename');

    if (originalElement) originalElement.textContent = originalFilename;
    if (compressedElement) compressedElement.textContent = compressedFilename;
}

// 格式化日期
function formatDateByPattern(date, pattern) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    switch (pattern) {
        case 'YYYYMMDD':
            return `${year}${month}${day}`;
        case 'YYYY-MM-DD':
            return `${year}-${month}-${day}`;
        case 'YYYY_MM_DD':
            return `${year}_${month}_${day}`;
        case 'DDMMYYYY':
            return `${day}${month}${year}`;
        default:
            return `${year}${month}${day}`;
    }
}

// 格式化时间
function formatTimeByPattern(date, pattern) {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    switch (pattern) {
        case 'HHMMSS':
            return `${hours}${minutes}${seconds}`;
        case 'HH-MM-SS':
            return `${hours}-${minutes}-${seconds}`;
        case 'HH_MM_SS':
            return `${hours}_${minutes}_${seconds}`;
        case 'HHMM':
            return `${hours}${minutes}`;
        default:
            return `${hours}${minutes}${seconds}`;
    }
}

// 更新压缩格式描述
function updateFormatDescription() {
    const format = document.getElementById('compression_format').value;
    const description = document.getElementById('format-description');

    const descriptions = {
        'zip': '通用性好，支持密码保护，兼容性最佳',
        'gzip': '压缩速度快，适合大文件，Linux系统常用',
        '7z': '压缩率最高，但速度较慢，适合长期存储'
    };

    description.textContent = descriptions[format] || '';
}

// 更新压缩级别信息
function updateCompressionLevel() {
    const level = document.getElementById('compression_level').value;
    const info = document.getElementById('compression-level-info');

    const levelInfo = {
        '1': { speed: '最快', ratio: '低', speedClass: 'success', ratioClass: 'warning' },
        '3': { speed: '快', ratio: '中等', speedClass: 'info', ratioClass: 'info' },
        '6': { speed: '中等', ratio: '好', speedClass: 'info', ratioClass: 'success' },
        '9': { speed: '慢', ratio: '最高', speedClass: 'warning', ratioClass: 'success' }
    };

    const current = levelInfo[level];
    if (current) {
        info.innerHTML = `
            <span class="badge bg-${current.speedClass} me-1">速度: ${current.speed}</span>
            <span class="badge bg-${current.ratioClass}">压缩率: ${current.ratio}</span>
        `;
    }
}

// 生成安全密码
function generatePassword() {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    document.getElementById('compression_password').value = password;
    showMessage('已生成安全密码', 'success', 2000);
}

// 重置基本设置
function resetGeneralSettings() {
    if (!confirm('确定要重置基本设置为默认值吗？')) return;

    const form = document.getElementById('generalForm');
    form.app_name.value = '数据库归档管理平台';
    form.default_batch_size.value = 1000;
    form.default_retention_days.value = 365;
    form.max_concurrent_tasks.value = 5;
    form.task_timeout.value = 60;
    form.auto_cleanup.checked = true;
    form.enable_monitoring.checked = true;
    form.auto_backup.checked = false;
    form.enable_api_rate_limit.checked = false;

    showMessage('基本设置已重置为默认值', 'info');
}

// 预览基本设置效果
function previewGeneralSettings() {
    const form = document.getElementById('generalForm');
    const formData = new FormData(form);

    const preview = `
        <div class="alert alert-info">
            <h6>设置预览</h6>
            <ul class="mb-0">
                <li>应用名称: ${formData.get('app_name')}</li>
                <li>默认批处理大小: ${formData.get('default_batch_size')} 条</li>
                <li>默认保留天数: ${formData.get('default_retention_days')} 天</li>
                <li>最大并发任务: ${formData.get('max_concurrent_tasks')} 个</li>
                <li>任务超时时间: ${formData.get('task_timeout')} 分钟</li>
                <li>自动清理: ${formData.has('auto_cleanup') ? '启用' : '禁用'}</li>
                <li>系统监控: ${formData.has('enable_monitoring') ? '启用' : '禁用'}</li>
            </ul>
        </div>
    `;

    // 创建临时预览元素
    const existingPreview = document.querySelector('.settings-preview');
    if (existingPreview) {
        existingPreview.remove();
    }

    const previewDiv = document.createElement('div');
    previewDiv.className = 'settings-preview mt-3';
    previewDiv.innerHTML = preview;

    document.getElementById('generalForm').appendChild(previewDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (previewDiv.parentNode) {
            previewDiv.remove();
        }
    }, 5000);
}

// 重置压缩设置
function resetCompressionSettings() {
    if (!confirm('确定要重置压缩设置为默认值吗？')) return;

    const form = document.getElementById('compressionForm');
    form.compression_enabled.checked = true;
    form.compression_format.value = 'zip';
    form.compression_level.value = '6';
    form.min_file_size.value = 10;
    form.compression_password.value = '';
    form.delete_original.checked = false;
    form.verify_compression.checked = true;
    form.split_large_files.checked = false;
    form.split_size.value = 100;

    updateFormatDescription();
    updateCompressionLevel();

    showMessage('压缩设置已重置为默认值', 'info');
}

// 显示压缩帮助
function showCompressionHelp() {
    const helpContent = `
        <div class="modal fade" id="compressionHelpModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">压缩设置帮助</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>压缩格式选择</h6>
                        <ul>
                            <li><strong>ZIP:</strong> 最通用的格式，支持密码保护，推荐使用</li>
                            <li><strong>GZIP:</strong> 压缩速度快，适合大文件，Linux系统常用</li>
                            <li><strong>7-Zip:</strong> 压缩率最高，但速度较慢，适合长期存储</li>
                        </ul>

                        <h6>压缩级别说明</h6>
                        <ul>
                            <li><strong>最快压缩:</strong> 速度最快，但压缩率较低</li>
                            <li><strong>快速压缩:</strong> 速度和压缩率的平衡点</li>
                            <li><strong>标准压缩:</strong> 推荐设置，综合性能最佳</li>
                            <li><strong>最佳压缩:</strong> 压缩率最高，但速度较慢</li>
                        </ul>

                        <h6>最佳实践建议</h6>
                        <ul>
                            <li>对于频繁访问的文件，建议使用快速压缩</li>
                            <li>对于长期存储的文件，建议使用最佳压缩</li>
                            <li>小于10MB的文件通常不需要压缩</li>
                            <li>启用文件完整性验证确保数据安全</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的帮助模态框
    const existingModal = document.getElementById('compressionHelpModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新的帮助模态框
    document.body.insertAdjacentHTML('beforeend', helpContent);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('compressionHelpModal'));
    modal.show();
}

// 导出所有设置
async function exportAllSettings() {
    try {
        showLoading();
        const response = await axios.get('/api/system/settings/export');

        if (response.data.success) {
            const blob = new Blob([JSON.stringify(response.data.data, null, 2)], {
                type: 'application/json'
            });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `archive_settings_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showMessage('设置导出成功', 'success');
        } else {
            showMessage('导出失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('导出设置失败:', error);
        showMessage('导出失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 导入设置
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async function(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            showLoading();
            const text = await file.text();
            const settings = JSON.parse(text);

            const response = await axios.post('/api/system/settings/import', settings);

            if (response.data.success) {
                showMessage('设置导入成功，页面将刷新', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showMessage('导入失败: ' + response.data.message, 'error');
            }
        } catch (error) {
            console.error('导入设置失败:', error);
            showMessage('导入失败: ' + error.message, 'error');
        } finally {
            hideLoading();
        }
    };
    input.click();
}

// 恢复默认设置
async function resetToDefaults() {
    const confirmed = await confirmDialog('确定要恢复所有设置为默认值吗？此操作不可撤销。');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.post('/api/system/settings/reset');

        if (response.data.success) {
            showMessage('设置已恢复为默认值，页面将刷新', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showMessage('恢复失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('恢复默认设置失败:', error);
        showMessage('恢复失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 验证所有设置
async function validateAllSettings() {
    try {
        showLoading();
        const response = await axios.post('/api/system/settings/validate');

        if (response.data.success) {
            const validation = response.data.data;
            let message = '设置验证完成:\n';
            message += `✓ 有效设置: ${validation.valid_count}\n`;
            message += `✗ 无效设置: ${validation.invalid_count}\n`;

            if (validation.warnings && validation.warnings.length > 0) {
                message += '\n警告:\n' + validation.warnings.join('\n');
            }

            showMessage(message, validation.invalid_count > 0 ? 'warning' : 'success');
        } else {
            showMessage('验证失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('验证设置失败:', error);
        showMessage('验证失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 加载系统设置
async function loadSystemSettings() {
    try {
        const response = await axios.get('/api/system/settings');
        const settings = response.data.data;

        // 填充基本设置
        if (settings.general) {
            const form = document.getElementById('generalForm');
            if (form) {
                Object.keys(settings.general).forEach(key => {
                    const field = form.elements[key];
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = settings.general[key];
                        } else {
                            field.value = settings.general[key];
                        }
                    }
                });
            }
        }

        // 填充压缩设置
        if (settings.compression) {
            const form = document.getElementById('compressionForm');
            if (form) {
                Object.keys(settings.compression).forEach(key => {
                    const field = form.elements[key];
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = settings.compression[key];
                        } else {
                            field.value = settings.compression[key];
                        }
                    }
                });
            }
        }

        // 填充文件命名设置
        if (settings.naming) {
            const form = document.getElementById('namingForm');
            if (form) {
                Object.keys(settings.naming).forEach(key => {
                    const field = form.elements[key];
                    if (field) {
                        field.value = settings.naming[key];
                    }
                });
            }
        }
    } catch (error) {
        console.error('加载系统设置失败:', error);
    }
}

// 显示通知配置对话框
function showNotificationConfigDialog(config = null) {
    const modal = new bootstrap.Modal(document.getElementById('notificationConfigModal'));
    const form = document.getElementById('notificationConfigForm');
    const title = document.getElementById('notificationConfigModalTitle');

    if (config) {
        title.innerHTML = '<i class="fas fa-edit me-2"></i>编辑通知配置';
        currentNotificationConfig = config;
        // 填充表单数据
        // ... 填充逻辑
    } else {
        title.innerHTML = '<i class="fas fa-plus me-2"></i>添加通知配置';
        currentNotificationConfig = null;
        form.reset();
    }

    toggleNotificationFields();
    modal.show();
}

// 切换通知字段显示
function toggleNotificationFields() {
    const notificationType = document.getElementById('notification_type').value;
    const emailFields = document.querySelector('.email-fields');
    const wechatFields = document.querySelector('.wechat-fields');
    const dingtalkFields = document.querySelector('.dingtalk-fields');

    // 隐藏所有字段
    if (emailFields) emailFields.style.display = 'none';
    if (wechatFields) wechatFields.style.display = 'none';
    if (dingtalkFields) dingtalkFields.style.display = 'none';

    // 显示对应字段
    switch (notificationType) {
        case 'email':
            if (emailFields) emailFields.style.display = 'block';
            break;
        case 'wechat':
            if (wechatFields) wechatFields.style.display = 'block';
            break;
        case 'dingtalk':
            if (dingtalkFields) dingtalkFields.style.display = 'block';
            break;
    }
}

// 保存通知配置
async function saveNotificationConfig() {
    const form = document.getElementById('notificationConfigForm');

    if (!validateForm(form)) {
        showMessage('请填写所有必填字段', 'error');
        return;
    }

    // 实现保存逻辑
    showMessage('通知配置保存成功', 'success');
}

// 测试通知配置
async function testNotificationConfig() {
    showMessage('正在发送测试通知...', 'info');
    // 实现测试逻辑
}

// 加载通知配置列表
async function loadNotificationConfigs() {
    try {
        const response = await axios.get('/api/notification-configs');
        const configs = response.data.data || [];

        renderNotificationConfigsTable(configs);
    } catch (error) {
        console.error('加载通知配置失败:', error);
    }
}

// 渲染通知配置表格
function renderNotificationConfigsTable(configs) {
    const tbody = document.getElementById('notification-configs-table');

    if (configs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-3">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无通知配置</div>
                    <button class="btn btn-primary btn-sm mt-2" onclick="showNotificationConfigDialog()">
                        <i class="fas fa-plus me-1"></i>添加第一个配置
                    </button>
                </td>
            </tr>
        `;
        return;
    }

    const html = configs.map(config => `
        <tr>
            <td>${config.name}</td>
            <td><span class="badge bg-info">${config.type.toUpperCase()}</span></td>
            <td>${config.triggers || '--'}</td>
            <td>
                <span class="status-badge status-${config.is_active ? 'success' : 'error'}">
                    ${config.is_active ? '启用' : '禁用'}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-success" onclick="testNotificationById(${config.id})" title="发送测试">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button class="btn btn-outline-primary" onclick="editNotificationConfig(${config.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteNotificationConfig(${config.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = html;
}

// 加载调度器状态
async function loadSchedulerStatus() {
    try {
        const response = await axios.get('/api/scheduler/status');
        const status = response.data.data;

        const statusElement = document.getElementById('scheduler-status');
        const jobsCountElement = document.getElementById('active-jobs-count');

        if (statusElement) {
            statusElement.className = `status-badge status-${status.running ? 'success' : 'error'}`;
            statusElement.innerHTML = `
                <i class="fas fa-${status.running ? 'check' : 'times'}-circle"></i>
                ${status.running ? '运行中' : '已停止'}
            `;
        }

        if (jobsCountElement) {
            jobsCountElement.textContent = status.active_jobs || 0;
        }

        renderSchedulerJobs(status.jobs || []);
    } catch (error) {
        console.error('加载调度器状态失败:', error);
    }
}

// 渲染调度器任务表格
function renderSchedulerJobs(jobs) {
    const tbody = document.getElementById('scheduler-jobs-table');

    if (jobs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-2">
                    <small class="text-muted">暂无调度任务</small>
                </td>
            </tr>
        `;
        return;
    }

    const html = jobs.map(job => `
        <tr>
            <td>${job.id}</td>
            <td>${job.name}</td>
            <td>${formatDateTime(job.next_run_time)}</td>
            <td><span class="badge bg-secondary">${job.trigger}</span></td>
            <td>
                <button class="btn btn-outline-warning btn-sm" onclick="pauseJob('${job.id}')" title="暂停">
                    <i class="fas fa-pause"></i>
                </button>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = html;
}

// 调度器控制函数
async function startScheduler() {
    try {
        const response = await axios.post('/api/scheduler/start');
        if (response.data.success) {
            showMessage('调度器已启动', 'success');
            loadSchedulerStatus();
        }
    } catch (error) {
        showMessage('启动调度器失败: ' + error.message, 'error');
    }
}

async function stopScheduler() {
    try {
        const response = await axios.post('/api/scheduler/stop');
        if (response.data.success) {
            showMessage('调度器已停止', 'warning');
            loadSchedulerStatus();
        }
    } catch (error) {
        showMessage('停止调度器失败: ' + error.message, 'error');
    }
}

async function restartScheduler() {
    try {
        const response = await axios.post('/api/scheduler/restart');
        if (response.data.success) {
            showMessage('调度器已重启', 'success');
            loadSchedulerStatus();
        }
    } catch (error) {
        showMessage('重启调度器失败: ' + error.message, 'error');
    }
}

function refreshSchedulerStatus() {
    loadSchedulerStatus();
}

// 保存调度器设置
async function saveSchedulerSettings() {
    const form = document.getElementById('schedulerForm');
    const formData = new FormData(form);

    const data = {
        max_workers: parseInt(formData.get('max_workers')),
        job_defaults_coalesce: formData.has('job_defaults_coalesce'),
        misfire_grace_time: parseInt(formData.get('misfire_grace_time')),
        max_instances: parseInt(formData.get('max_instances'))
    };

    try {
        showLoading();
        const response = await axios.post('/api/scheduler/settings', data);

        if (response.data.success) {
            showMessage('调度器设置保存成功', 'success');
        } else {
            showMessage('保存失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存调度器设置失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 保存安全设置
async function saveSecuritySettings() {
    const form = document.getElementById('securityForm');
    const formData = new FormData(form);

    const data = {
        min_password_length: parseInt(formData.get('min_password_length')),
        password_expiry_days: parseInt(formData.get('password_expiry_days')),
        require_special_chars: formData.has('require_special_chars'),
        session_timeout: parseInt(formData.get('session_timeout')),
        max_login_attempts: parseInt(formData.get('max_login_attempts')),
        allowed_ips: formData.get('allowed_ips'),
        encryption_algorithm: formData.get('encryption_algorithm'),
        encrypt_database_passwords: formData.has('encrypt_database_passwords'),
        encrypt_export_files: formData.has('encrypt_export_files')
    };

    try {
        showLoading();
        const response = await axios.post('/api/system/settings/security', data);

        if (response.data.success) {
            showMessage('安全设置保存成功', 'success');
        } else {
            showMessage('保存失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存安全设置失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 重新生成加密密钥
async function regenerateEncryptionKey() {
    const confirmed = await confirmDialog('重新生成加密密钥将使所有已加密的数据无法解密，确定继续吗？');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.post('/api/system/security/regenerate-key');

        if (response.data.success) {
            showMessage('加密密钥已重新生成', 'success');
        } else {
            showMessage('生成失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('重新生成加密密钥失败:', error);
        showMessage('生成失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 保存日志设置
async function saveLogsSettings() {
    const form = document.getElementById('logsForm');
    const formData = new FormData(form);

    const data = {
        log_level: formData.get('log_level'),
        log_retention_days: parseInt(formData.get('log_retention_days')),
        max_log_file_size: parseInt(formData.get('max_log_file_size')),
        log_backup_count: parseInt(formData.get('log_backup_count')),
        enable_sql_logging: formData.has('enable_sql_logging'),
        enable_performance_logging: formData.has('enable_performance_logging'),
        enable_audit_logging: formData.has('enable_audit_logging')
    };

    try {
        showLoading();
        const response = await axios.post('/api/system/settings/logs', data);

        if (response.data.success) {
            showMessage('日志设置保存成功', 'success');
        } else {
            showMessage('保存失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存日志设置失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 下载日志
function downloadLogs() {
    window.open('/api/system/logs/download', '_blank');
}

// 清空日志
async function clearLogs() {
    const confirmed = await confirmDialog('确定要清空所有日志吗？此操作不可撤销。');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.post('/api/system/logs/clear');

        if (response.data.success) {
            showMessage('日志已清空', 'success');
        } else {
            showMessage('清空失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('清空日志失败:', error);
        showMessage('清空失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}
</script>
{% endblock %}