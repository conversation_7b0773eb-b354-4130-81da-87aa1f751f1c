from flask import request, jsonify, current_app
from app import db
from app.models import UploadConfig, ArchiveTask, SystemLog
from app.api import api_bp
from app.utils.crypto import get_encryption_key

@api_bp.route('/upload-configs', methods=['GET'])
def get_upload_configs():
    """获取上传配置列表"""
    try:
        task_id = request.args.get('task_id', type=int)
        
        query = UploadConfig.query
        if task_id:
            query = query.filter_by(task_id=task_id)
        
        configs = query.all()
        return jsonify({
            'success': True,
            'data': [config.to_dict() for config in configs]
        })
    except Exception as e:
        SystemLog.log_error('upload_configs', f'获取上传配置列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取上传配置列表失败: {str(e)}'
        }), 500

@api_bp.route('/upload-configs', methods=['POST'])
def create_upload_config():
    """创建上传配置"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['task_id', 'upload_type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查任务是否存在
        task = ArchiveTask.query.get(data['task_id'])
        if not task:
            return jsonify({
                'success': False,
                'message': '指定的任务不存在'
            }), 400
        
        # 创建上传配置
        config = UploadConfig(
            task_id=data['task_id'],
            upload_type=data['upload_type'],
            is_default=data.get('is_default', False),
            host=data.get('host'),
            port=data.get('port'),
            username=data.get('username'),
            remote_path=data.get('remote_path'),
            local_path=data.get('local_path'),
            passive_mode=data.get('passive_mode', True),
            is_active=data.get('is_active', True)
        )
        
        # 设置密码
        if data.get('password'):
            encryption_key = get_encryption_key()
            config.set_password(data['password'], encryption_key)
        
        # 设置额外配置
        if data.get('config'):
            config.set_config(data['config'])
        
        db.session.add(config)
        db.session.commit()
        
        SystemLog.log_info('upload_configs', f'创建上传配置: {config.upload_type}')
        
        return jsonify({
            'success': True,
            'message': '上传配置创建成功',
            'data': config.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('upload_configs', f'创建上传配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'创建上传配置失败: {str(e)}'
        }), 500

@api_bp.route('/upload-configs/<int:config_id>', methods=['PUT'])
def update_upload_config(config_id):
    """更新上传配置"""
    try:
        config = UploadConfig.query.get_or_404(config_id)
        data = request.get_json()

        # 更新字段
        updatable_fields = [
            'upload_type', 'is_default', 'host', 'port', 'username',
            'remote_path', 'local_path', 'passive_mode', 'is_active'
        ]

        for field in updatable_fields:
            if field in data:
                setattr(config, field, data[field])

        # 如果提供了新密码，则更新密码
        if 'password' in data and data['password']:
            encryption_key = get_encryption_key()
            config.set_password(data['password'], encryption_key)

        # 设置额外配置
        if 'config' in data:
            config.set_config(data['config'])

        db.session.commit()

        SystemLog.log_info('upload_configs', f'更新上传配置: {config.upload_type}')

        return jsonify({
            'success': True,
            'message': '上传配置更新成功',
            'data': config.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('upload_configs', f'更新上传配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'更新上传配置失败: {str(e)}'
        }), 500

@api_bp.route('/upload-configs/<int:config_id>', methods=['DELETE'])
def delete_upload_config(config_id):
    """删除上传配置"""
    try:
        config = UploadConfig.query.get_or_404(config_id)

        config_type = config.upload_type
        db.session.delete(config)
        db.session.commit()

        SystemLog.log_info('upload_configs', f'删除上传配置: {config_type}')

        return jsonify({
            'success': True,
            'message': '上传配置删除成功'
        })

    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('upload_configs', f'删除上传配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除上传配置失败: {str(e)}'
        }), 500

@api_bp.route('/upload-configs/<int:config_id>/test', methods=['POST'])
def test_upload_config(config_id):
    """测试上传配置"""
    try:
        config = UploadConfig.query.get_or_404(config_id)
        encryption_key = get_encryption_key()

        success, message = config.test_connection(encryption_key)

        SystemLog.log_info('upload_configs', f'测试上传配置: {config.upload_type} - {message}')

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        SystemLog.log_error('upload_configs', f'测试上传配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'测试上传配置失败: {str(e)}'
        }), 500
