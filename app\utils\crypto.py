from cryptography.fernet import Fernet
from flask import current_app
import base64
import os

def generate_key():
    """生成新的加密密钥"""
    return Fernet.generate_key().decode()

def get_encryption_key():
    """获取加密密钥"""
    config = current_app.config.get('ARCHIVE_CONFIG', {})
    key = config.get('security', {}).get('encryption_key')
    
    if not key or key == 'your-encryption-key-change-in-production':
        # 如果没有配置密钥或使用默认密钥，生成一个新的
        key = generate_key()
        # 在生产环境中，应该将这个密钥保存到配置文件中
        current_app.logger.warning('使用临时生成的加密密钥，请在配置文件中设置固定密钥')
    
    # 确保密钥格式正确
    try:
        # 如果密钥不是base64格式，尝试编码
        if len(key) != 44:  # Fernet密钥的标准长度
            key = base64.urlsafe_b64encode(key.encode()[:32].ljust(32, b'\0')).decode()
        
        # 验证密钥是否有效
        Fernet(key.encode())
        return key
    except Exception:
        # 如果密钥无效，生成一个新的
        key = generate_key()
        current_app.logger.warning('原密钥无效，使用新生成的临时密钥')
        return key

def encrypt_password(password, key=None):
    """加密密码"""
    if key is None:
        key = get_encryption_key()
    
    fernet = Fernet(key.encode())
    encrypted_password = fernet.encrypt(password.encode())
    return base64.b64encode(encrypted_password).decode()

def decrypt_password(encrypted_password, key=None):
    """解密密码"""
    if key is None:
        key = get_encryption_key()
    
    try:
        fernet = Fernet(key.encode())
        encrypted_data = base64.b64decode(encrypted_password.encode())
        return fernet.decrypt(encrypted_data).decode()
    except Exception as e:
        raise ValueError(f"密码解密失败: {str(e)}")

def hash_password(password, salt=None):
    """哈希密码（用于用户认证）"""
    import bcrypt
    
    if salt is None:
        config = current_app.config.get('ARCHIVE_CONFIG', {})
        salt = config.get('security', {}).get('password_salt', 'default-salt').encode()
    
    if isinstance(salt, str):
        salt = salt.encode()
    
    return bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()

def verify_password(password, hashed_password):
    """验证密码"""
    import bcrypt
    
    try:
        return bcrypt.checkpw(password.encode(), hashed_password.encode())
    except Exception:
        return False
