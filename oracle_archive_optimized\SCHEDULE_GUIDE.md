# 定时计划任务指南

## 功能概述

Oracle 分布式归档系统支持为每个表设置独立的定时计划，实现自动化归档任务执行。

## 主要特性

1. **灵活的调度选项**

   - 每天定时执行
   - 每周特定日期执行
   - 每月特定日期执行
   - 自定义 Cron 表达式

2. **独立配置**

   - 每个表可以有不同的执行时间
   - 可以随时启用/禁用
   - 支持修改和删除

3. **智能调度**
   - 自动计算下次执行时间
   - 记录上次执行时间
   - 避免任务重复

## 使用方法

### 1. 设置定时计划

1. 进入"表配置"页面
2. 找到需要设置计划的表
3. 点击时钟图标（⏰）
4. 选择执行频率：
   - **每天**：选择具体时间（如：02:00）
   - **每周**：选择星期几和时间
   - **每月**：选择每月第几天和时间
   - **自定义**：输入标准 Cron 表达式

### 2. <PERSON>ron 表达式格式

```
秒 分 时 日 月 周
0  2  *  *  *  *    # 每天凌晨2点
0  0  2  *  *  0    # 每周日凌晨2点
0  0  3  1  *  *    # 每月1号凌晨3点
```

常用示例：

- `0 0 2 * * *` - 每天凌晨 2 点
- `0 30 1 * * *` - 每天凌晨 1 点 30 分
- `0 0 23 * * 5` - 每周五晚上 11 点
- `0 0 2 1,15 * *` - 每月 1 号和 15 号凌晨 2 点

### 3. 最佳实践

#### 时间选择

- 选择业务低峰期（通常是凌晨 2-5 点）
- 不同表的执行时间最好错开 15-30 分钟
- 考虑数据量大小合理安排时间

#### 频率设置

- 日志类表：建议每天归档
- 业务数据表：可以每周或每月归档
- 重要数据：建议先测试后再设置自动执行

#### 监控建议

- 定期查看任务执行情况
- 关注失败任务并及时处理
- 根据执行时长调整计划时间

## 注意事项

1. **时区问题**

   - 所有时间均为服务器时区
   - 跨时区部署需要特别注意

2. **资源竞争**

   - 避免多个大表同时执行
   - 考虑数据库负载情况

3. **依赖关系**
   - 如果表之间有关联，注意执行顺序
   - 可以通过时间间隔来控制顺序

## 故障处理

### 任务未执行

1. 检查定时计划是否启用
2. 检查 Agent 是否在线
3. 查看中央服务日志

### 执行时间不准

1. 检查服务器时间是否正确
2. 确认 Cron 表达式是否正确
3. 查看调度器状态

### 任务重复执行

1. 检查是否有多个相同的计划
2. 确认调度器是否正常
3. 查看任务历史记录

## API 参考

### 创建/更新计划

```bash
POST /api/schedule/create
{
    "table_id": 1,
    "cron_expression": "0 0 2 * * *",
    "enabled": true
}
```

### 获取计划

```bash
GET /api/schedule/get/{table_id}
```

### 删除计划

```bash
POST /api/schedule/delete
{
    "table_id": 1
}
```

## 总结

定时计划功能让归档任务完全自动化，减少人工干预，提高运维效率。合理配置定时计划，可以确保数据及时归档，同时不影响业务系统性能。
