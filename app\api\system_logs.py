from flask import request, jsonify, current_app
from app import db
from app.models import SystemLog
from app.api import api_bp
from datetime import datetime, timedelta

@api_bp.route('/system-logs', methods=['GET'])
def get_system_logs():
    """获取系统日志列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        level = request.args.get('level')
        module = request.args.get('module')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 构建查询
        query = SystemLog.query
        
        if level:
            query = query.filter(SystemLog.level == level)
        
        if module:
            query = query.filter(SystemLog.module == module)
        
        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date)
                query = query.filter(SystemLog.created_at >= start_dt)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date)
                query = query.filter(SystemLog.created_at <= end_dt)
            except ValueError:
                pass
        
        # 按创建时间倒序排列
        query = query.order_by(SystemLog.created_at.desc())
        
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return jsonify({
            'success': True,
            'data': {
                'items': [log.to_dict() for log in pagination.items],
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取系统日志失败: {str(e)}'
        }), 500

@api_bp.route('/system-logs/cleanup', methods=['POST'])
def cleanup_system_logs():
    """清理系统日志"""
    try:
        data = request.get_json()
        days = data.get('days', 30)  # 默认清理30天前的日志
        
        deleted_count = SystemLog.cleanup_old_logs(days)
        
        SystemLog.log_info('system_logs', f'清理了 {deleted_count} 条旧日志记录')
        
        return jsonify({
            'success': True,
            'message': f'成功清理了 {deleted_count} 条旧日志记录'
        })
        
    except Exception as e:
        SystemLog.log_error('system_logs', f'清理系统日志失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'清理系统日志失败: {str(e)}'
        }), 500

@api_bp.route('/system-logs/levels', methods=['GET'])
def get_log_levels():
    """获取日志级别列表"""
    try:
        levels = db.session.query(SystemLog.level).distinct().all()
        level_list = [level[0] for level in levels]
        
        return jsonify({
            'success': True,
            'data': level_list
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取日志级别失败: {str(e)}'
        }), 500

@api_bp.route('/system-logs/modules', methods=['GET'])
def get_log_modules():
    """获取日志模块列表"""
    try:
        modules = db.session.query(SystemLog.module).distinct().all()
        module_list = [module[0] for module in modules]
        
        return jsonify({
            'success': True,
            'data': module_list
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取日志模块失败: {str(e)}'
        }), 500
