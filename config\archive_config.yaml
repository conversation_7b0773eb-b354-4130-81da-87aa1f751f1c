app:
  name: "数据库归档管理平台"
  host: "0.0.0.0"
  port: 8080
  debug: true
  secret_key: "dev-secret-key-change-in-production"

database:
  url: "sqlite:///data/archive_platform.db"
  echo: false

logging:
  level: "INFO"
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

security:
  encryption_key: "dev-encryption-key-change-in-production"
  password_salt: "dev-password-salt-change-in-production"

archive:
  temp_dir: "temp"
  backup_dir: "backups"
  max_concurrent_tasks: 3
  batch_delete_size: 5000
  compression_level: 6

notification:
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    use_tls: true
    from_address: ""

  wechat:
    enabled: false
    webhook_url: ""

  dingtalk:
    enabled: false
    webhook_url: ""
    secret: ""

upload:
  default_type: "local"  # local, sftp, ftp
  retry_count: 3
  timeout: 300

  sftp:
    host: ""
    port: 22
    username: ""
    password: ""
    remote_path: "/backup"

  ftp:
    host: ""
    port: 21
    username: ""
    password: ""
    remote_path: "/backup"
    passive: true
