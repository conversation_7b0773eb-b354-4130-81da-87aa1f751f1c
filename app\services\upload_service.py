import os
import shutil
import paramiko
import ftplib
from flask import current_app
from app.models import UploadConfig, SystemLog
from app.utils.crypto import get_encryption_key

class UploadService:
    """文件上传服务类"""
    
    def __init__(self):
        self.config = current_app.config.get('ARCHIVE_CONFIG', {})
        self.upload_config = self.config.get('upload', {})
    
    def upload_file(self, task_id, file_path):
        """上传文件"""
        try:
            # 获取任务的上传配置
            upload_configs = UploadConfig.query.filter_by(
                task_id=task_id,
                is_active=True
            ).all()
            
            if not upload_configs:
                SystemLog.log_warning('upload_service', f'任务 {task_id} 没有配置上传设置')
                return True  # 没有配置上传，视为成功
            
            success_count = 0
            total_configs = len(upload_configs)
            
            for config in upload_configs:
                try:
                    if config.upload_type == 'local':
                        success = self._upload_to_local(config, file_path)
                    elif config.upload_type == 'sftp':
                        success = self._upload_to_sftp(config, file_path)
                    elif config.upload_type == 'ftp':
                        success = self._upload_to_ftp(config, file_path)
                    else:
                        SystemLog.log_error('upload_service', f'不支持的上传类型: {config.upload_type}')
                        continue
                    
                    if success:
                        success_count += 1
                        SystemLog.log_info('upload_service', f'文件上传成功: {config.upload_type} - {file_path}')
                    else:
                        SystemLog.log_error('upload_service', f'文件上传失败: {config.upload_type} - {file_path}')
                        
                except Exception as e:
                    SystemLog.log_error('upload_service', f'文件上传异常: {config.upload_type} - {str(e)}')
            
            # 如果至少有一个配置上传成功，就认为成功
            return success_count > 0
            
        except Exception as e:
            SystemLog.log_error('upload_service', f'上传文件失败: {str(e)}')
            return False
    
    def _upload_to_local(self, config, file_path):
        """上传到本地目录"""
        try:
            # 获取目标路径
            target_dir = config.local_path or config.remote_path
            if not target_dir:
                SystemLog.log_error('upload_service', '本地上传路径未配置')
                return False
            
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)
            
            # 获取文件名
            filename = os.path.basename(file_path)
            target_path = os.path.join(target_dir, filename)
            
            # 复制文件
            shutil.copy2(file_path, target_path)
            
            SystemLog.log_info('upload_service', f'文件复制到本地: {target_path}')
            return True
            
        except Exception as e:
            SystemLog.log_error('upload_service', f'本地文件复制失败: {str(e)}')
            return False
    
    def _upload_to_sftp(self, config, file_path):
        """上传到SFTP服务器"""
        try:
            encryption_key = get_encryption_key()
            password = config.get_password(encryption_key)
            
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接服务器
            ssh.connect(
                hostname=config.host,
                port=config.port or 22,
                username=config.username,
                password=password,
                timeout=self.upload_config.get('timeout', 300)
            )
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 确保远程目录存在
            remote_dir = config.remote_path or '.'
            try:
                sftp.listdir(remote_dir)
            except FileNotFoundError:
                # 尝试创建目录
                self._create_remote_directory(sftp, remote_dir)
            
            # 获取文件名和远程路径
            filename = os.path.basename(file_path)
            remote_file_path = f"{remote_dir.rstrip('/')}/{filename}"
            
            # 上传文件
            sftp.put(file_path, remote_file_path)
            
            # 关闭连接
            sftp.close()
            ssh.close()
            
            SystemLog.log_info('upload_service', f'SFTP上传成功: {remote_file_path}')
            return True
            
        except Exception as e:
            SystemLog.log_error('upload_service', f'SFTP上传失败: {str(e)}')
            return False
    
    def _upload_to_ftp(self, config, file_path):
        """上传到FTP服务器"""
        try:
            encryption_key = get_encryption_key()
            password = config.get_password(encryption_key)
            
            # 创建FTP连接
            ftp = ftplib.FTP()
            ftp.connect(
                config.host,
                config.port or 21,
                timeout=self.upload_config.get('timeout', 300)
            )
            ftp.login(config.username, password)
            
            # 设置被动模式
            if config.passive_mode:
                ftp.set_pasv(True)
            else:
                ftp.set_pasv(False)
            
            # 切换到远程目录
            remote_dir = config.remote_path or '.'
            if remote_dir != '.':
                try:
                    ftp.cwd(remote_dir)
                except ftplib.error_perm:
                    # 尝试创建目录
                    self._create_ftp_directory(ftp, remote_dir)
                    ftp.cwd(remote_dir)
            
            # 获取文件名
            filename = os.path.basename(file_path)
            
            # 上传文件
            with open(file_path, 'rb') as file:
                ftp.storbinary(f'STOR {filename}', file)
            
            # 关闭连接
            ftp.quit()
            
            SystemLog.log_info('upload_service', f'FTP上传成功: {remote_dir}/{filename}')
            return True
            
        except Exception as e:
            SystemLog.log_error('upload_service', f'FTP上传失败: {str(e)}')
            return False
    
    def _create_remote_directory(self, sftp, remote_dir):
        """创建SFTP远程目录"""
        try:
            dirs = remote_dir.strip('/').split('/')
            current_dir = ''
            
            for dir_name in dirs:
                if not dir_name:
                    continue
                
                current_dir = f"{current_dir}/{dir_name}" if current_dir else dir_name
                
                try:
                    sftp.listdir(current_dir)
                except FileNotFoundError:
                    sftp.mkdir(current_dir)
                    
        except Exception as e:
            SystemLog.log_error('upload_service', f'创建SFTP目录失败: {str(e)}')
            raise
    
    def _create_ftp_directory(self, ftp, remote_dir):
        """创建FTP远程目录"""
        try:
            dirs = remote_dir.strip('/').split('/')
            
            for dir_name in dirs:
                if not dir_name:
                    continue
                
                try:
                    ftp.cwd(dir_name)
                except ftplib.error_perm:
                    ftp.mkd(dir_name)
                    ftp.cwd(dir_name)
                    
        except Exception as e:
            SystemLog.log_error('upload_service', f'创建FTP目录失败: {str(e)}')
            raise
    
    def upload_with_retry(self, task_id, file_path):
        """带重试的文件上传"""
        retry_count = self.upload_config.get('retry_count', 3)
        
        for attempt in range(retry_count):
            try:
                success = self.upload_file(task_id, file_path)
                if success:
                    return True
                
                if attempt < retry_count - 1:
                    SystemLog.log_warning('upload_service', f'文件上传失败，第 {attempt + 1} 次重试')
                    
            except Exception as e:
                SystemLog.log_error('upload_service', f'文件上传重试失败 (第 {attempt + 1} 次): {str(e)}')
                
                if attempt < retry_count - 1:
                    import time
                    time.sleep(2 ** attempt)  # 指数退避
        
        SystemLog.log_error('upload_service', f'文件上传最终失败，已重试 {retry_count} 次')
        return False
    
    def get_upload_status(self, task_id):
        """获取上传状态"""
        try:
            configs = UploadConfig.query.filter_by(
                task_id=task_id,
                is_active=True
            ).all()
            
            status = {
                'total_configs': len(configs),
                'configs': []
            }
            
            for config in configs:
                config_status = {
                    'id': config.id,
                    'type': config.upload_type,
                    'host': config.host,
                    'path': config.remote_path or config.local_path,
                    'is_active': config.is_active
                }
                status['configs'].append(config_status)
            
            return status
            
        except Exception as e:
            SystemLog.log_error('upload_service', f'获取上传状态失败: {str(e)}')
            return None
