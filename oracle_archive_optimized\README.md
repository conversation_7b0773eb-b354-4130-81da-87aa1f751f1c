# Oracle 分布式归档系统（优化版）

## 系统架构

本系统采用分布式架构，主要组件包括：

1. **中央控制服务** (Python/Flask) - 部署在 VPS 上

   - Web 管理界面
   - 任务调度和分发
   - 状态监控
   - 文件存储管理

2. **Shell Agent** (Bash 脚本) - 部署在 Oracle 数据库服务器上
   - 执行归档任务
   - 数据导出和压缩
   - 文件上传
   - 状态报告

## 系统特点

- **轻量级 Agent**: 使用 Shell 脚本，无需 Python 环境
- **SSH 远程执行**: 通过 SSH 安全执行任务
- **灵活配置**: 支持多种压缩和上传方式
- **实时监控**: Web 界面实时查看任务状态
- **自动化调度**: 支持定时自动执行归档任务

## 快速开始

### 1. 部署中央控制服务（在 VPS 上）

```bash
# 安装Python依赖
cd central_server
pip install -r requirements.txt

# 配置中央服务
cp central_config.yaml.example central_config.yaml
vim central_config.yaml

# 启动服务
python central_control.py
```

### 2. 部署 Shell Agent（在数据库服务器上）

```bash
# 使用部署脚本
cd deploy_tools
sudo ./deploy_agent.sh install

# 或手动部署
mkdir -p /opt/oracle_archive_agent
cp shell_agents/archive_agent.sh /opt/oracle_archive_agent/
cp shell_agents/agent.conf.template /opt/oracle_archive_agent/agent.conf
chmod +x /opt/oracle_archive_agent/archive_agent.sh
```

### 3. 配置 SSH 密钥认证

在中央服务器上：

```bash
# 生成SSH密钥（如果没有）
ssh-keygen -t rsa -b 2048

# 将公钥复制到各个数据库服务器
ssh-copy-id oracle@db-server-1
ssh-copy-id oracle@db-server-2
```

### 4. 注册 Agent

在 Web 界面中注册各个 Agent，或使用 API：

```bash
curl -X POST http://central-server:8080/api/agent/register \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "oracle_agent_01",
    "host": "db-server-1",
    "ssh_port": 22,
    "ssh_user": "oracle",
    "oracle_host": "localhost",
    "oracle_port": 1521,
    "oracle_sid": "ORCL"
  }'
```

## 配置说明

### Agent 配置 (agent.conf)

```bash
# Agent标识
AGENT_ID="oracle_agent_01"

# Oracle数据库配置
ORACLE_HOME="/u01/app/oracle/product/19.0.0/dbhome_1"
ORACLE_SID="ORCL"
DB_USER="archive_user"
DB_PASS="archive_password"

# 中央控制服务器
CENTRAL_SERVER="http://central-server:8080"

# 上传配置
UPLOAD_TYPE="scp"  # scp, sftp, http
UPLOAD_SERVER="backup-server"
UPLOAD_PATH="/backup/oracle_archives"

# 压缩配置
COMPRESSION_TYPE="gzip"  # gzip, zip, tar.gz
COMPRESSION_LEVEL="6"

# 归档配置
DELETE_AFTER_ARCHIVE="false"  # 是否在归档后删除原数据
```

### 中央服务配置 (central_config.yaml)

```yaml
server:
  host: "0.0.0.0"
  port: 8080

storage:
  type: "local"
  local_path: "./central_archives"

ssh:
  key_file: "~/.ssh/id_rsa"
  default_port: 22
  timeout: 300

scheduler:
  enabled: true
  check_interval: 60
```

## 使用方法

### 1. 添加归档表配置

在 Web 界面中配置需要归档的表：

- 表名和 Schema
- 日期字段
- 数据保留天数
- 批处理大小

### 2. 创建归档任务

- 手动创建：在 Web 界面点击"创建任务"
- 自动调度：配置调度器自动创建任务

### 3. 监控任务执行

- 查看任务列表和状态
- 查看 Agent 状态
- 查看归档文件

## 运维命令

### Agent 管理

```bash
# 启动Agent
systemctl start oracle-archive-agent

# 停止Agent
systemctl stop oracle-archive-agent

# 查看状态
systemctl status oracle-archive-agent

# 查看日志
journalctl -u oracle-archive-agent -f

# 测试配置
/opt/oracle_archive_agent/archive_agent.sh test
```

### 中央服务管理

```bash
# 启动服务
python central_control.py

# 使用systemd管理（需要创建service文件）
systemctl start oracle-archive-central
```

## 故障排查

### Agent 连接问题

1. 检查 SSH 连接

```bash
ssh oracle@db-server
```

2. 检查 Agent 配置

```bash
cat /opt/oracle_archive_agent/agent.conf
```

3. 查看 Agent 日志

```bash
tail -f /opt/oracle_archive_agent/logs/archive_agent_*.log
```

### 数据库连接问题

1. 测试 Oracle 连接

```bash
/opt/oracle_archive_agent/archive_agent.sh test
```

2. 检查环境变量

```bash
echo $ORACLE_HOME
echo $ORACLE_SID
```

### 任务执行失败

1. 查看任务日志
2. 检查磁盘空间
3. 验证权限设置
4. 测试网络连接

## 安全建议

1. 使用 SSH 密钥认证，禁用密码登录
2. 限制 SSH 访问源 IP
3. 定期更新系统和依赖包
4. 使用加密传输归档文件
5. 设置适当的文件权限
6. 定期审计归档操作日志

## 性能优化

1. 调整批处理大小
2. 使用并行压缩
3. 优化网络传输
4. 合理设置任务调度时间
5. 监控系统资源使用

## 扩展功能

系统支持以下扩展：

- 自定义压缩算法
- 多种存储后端（S3、NFS 等）
- 邮件通知
- 监控告警
- 数据加密
- 增量归档

## License

MIT License
