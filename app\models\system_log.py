from app import db
from datetime import datetime

class SystemLog(db.Model):
    """系统日志模型"""
    __tablename__ = 'system_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 日志基本信息
    level = db.Column(db.String(10), nullable=False, comment='日志级别')  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    module = db.Column(db.String(50), nullable=False, comment='模块名称')
    message = db.Column(db.Text, nullable=False, comment='日志消息')
    
    # 关联信息
    task_id = db.Column(db.Integer, comment='关联任务ID')
    execution_id = db.Column(db.Integer, comment='关联执行ID')
    user_id = db.Column(db.String(50), comment='用户ID')
    
    # 额外信息
    details = db.Column(db.Text, comment='详细信息')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.String(500), comment='用户代理')
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间', index=True)
    
    def __repr__(self):
        return f'<SystemLog {self.level}:{self.module}>'
    
    @classmethod
    def log_info(cls, module, message, **kwargs):
        """记录INFO级别日志"""
        return cls._create_log('INFO', module, message, **kwargs)
    
    @classmethod
    def log_warning(cls, module, message, **kwargs):
        """记录WARNING级别日志"""
        return cls._create_log('WARNING', module, message, **kwargs)
    
    @classmethod
    def log_error(cls, module, message, **kwargs):
        """记录ERROR级别日志"""
        return cls._create_log('ERROR', module, message, **kwargs)
    
    @classmethod
    def log_debug(cls, module, message, **kwargs):
        """记录DEBUG级别日志"""
        return cls._create_log('DEBUG', module, message, **kwargs)
    
    @classmethod
    def _create_log(cls, level, module, message, **kwargs):
        """创建日志记录"""
        log = cls(
            level=level,
            module=module,
            message=message,
            task_id=kwargs.get('task_id'),
            execution_id=kwargs.get('execution_id'),
            user_id=kwargs.get('user_id'),
            details=kwargs.get('details'),
            ip_address=kwargs.get('ip_address'),
            user_agent=kwargs.get('user_agent')
        )
        
        try:
            db.session.add(log)
            db.session.commit()
            return log
        except Exception as e:
            db.session.rollback()
            # 如果数据库记录失败，至少打印到控制台
            print(f"[{level}] {module}: {message} (数据库记录失败: {str(e)})")
            return None
    
    @classmethod
    def cleanup_old_logs(cls, days=30):
        """清理旧日志"""
        from datetime import timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        try:
            deleted_count = cls.query.filter(cls.created_at < cutoff_date).delete()
            db.session.commit()
            return deleted_count
        except Exception as e:
            db.session.rollback()
            raise e
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'level': self.level,
            'module': self.module,
            'message': self.message,
            'task_id': self.task_id,
            'execution_id': self.execution_id,
            'user_id': self.user_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
