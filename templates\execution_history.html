{% extends "base.html" %}

{% block title %}执行历史 - 数据库归档管理平台{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">执行历史</li>
{% endblock %}

{% block page_title %}执行历史{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <button class="btn btn-outline-secondary btn-icon" onclick="refreshHistory()">
        <i class="fas fa-sync-alt"></i>
        <span>刷新</span>
    </button>
    <button class="btn btn-outline-info btn-icon" onclick="exportHistory()">
        <i class="fas fa-download"></i>
        <span>导出</span>
    </button>
</div>
{% endblock %}

{% block content %}
<!-- 执行统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-play-circle fa-2x text-primary mb-2"></i>
                <h4 class="mb-0" id="total-executions">0</h4>
                <small class="text-muted">总执行次数</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4 class="mb-0" id="success-executions">0</h4>
                <small class="text-muted">成功执行</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                <h4 class="mb-0" id="failed-executions">0</h4>
                <small class="text-muted">执行失败</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4 class="mb-0" id="running-executions">0</h4>
                <small class="text-muted">正在执行</small>
            </div>
        </div>
    </div>
</div>

<!-- 筛选器 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>筛选条件
        </h5>
    </div>
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="task_filter" class="form-label">任务</label>
                <select class="form-select" id="task_filter" name="task_id">
                    <option value="">全部任务</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status_filter" class="form-label">状态</label>
                <select class="form-select" id="status_filter" name="status">
                    <option value="">全部状态</option>
                    <option value="running">运行中</option>
                    <option value="success">成功</option>
                    <option value="failed">失败</option>
                    <option value="cancelled">已取消</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="date_from" name="date_from">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="date_to" name="date_to">
            </div>
            <div class="col-12">
                <button type="button" class="btn btn-primary" onclick="applyFilter()">
                    <i class="fas fa-search me-1"></i>筛选
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearFilter()">
                    <i class="fas fa-times me-1"></i>清除
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 执行历史列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-history me-2"></i>执行记录
        </h5>
        <div class="d-flex align-items-center gap-2">
            <span class="text-muted">每页显示:</span>
            <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                <option value="10">10</option>
                <option value="25" selected>25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>执行ID</th>
                        <th>任务名称</th>
                        <th>状态</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>持续时间</th>
                        <th>处理记录数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="history-table-body">
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载执行历史...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <nav>
            <ul class="pagination pagination-sm mb-0" id="pagination">
                <!-- 分页将在这里动态生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 执行详情模态框 -->
<div class="modal fade" id="executionDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>执行详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="executionDetailContent">
                <!-- 执行详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="downloadExecutionLog()">
                    <i class="fas fa-download me-1"></i>下载日志
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentPage = 1;
let pageSize = 25;
let totalPages = 1;
let currentFilters = {};
let currentExecutionId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadExecutionHistory();
    loadTasksForFilter();
    updateExecutionStats();
    
    // 设置默认日期范围（最近30天）
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    document.getElementById('date_to').value = today.toISOString().split('T')[0];
    document.getElementById('date_from').value = thirtyDaysAgo.toISOString().split('T')[0];
});

// 加载执行历史
async function loadExecutionHistory(page = 1) {
    try {
        const params = new URLSearchParams({
            page: page,
            per_page: pageSize,
            ...currentFilters
        });
        
        const response = await axios.get(`/api/task-executions?${params}`);
        const data = response.data.data;
        
        renderHistoryTable(data.items);
        renderPagination(data.pagination);
        updateExecutionStats();
        
        currentPage = page;
    } catch (error) {
        console.error('加载执行历史失败:', error);
        showMessage('加载执行历史失败: ' + error.message, 'error');
    }
}

// 渲染历史表格
function renderHistoryTable(executions) {
    const tbody = document.getElementById('history-table-body');
    
    if (executions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无执行记录</div>
                </td>
            </tr>
        `;
        return;
    }
    
    const html = executions.map(exec => `
        <tr>
            <td>
                <div class="fw-bold">#${exec.id}</div>
                <small class="text-muted">${exec.execution_id || '--'}</small>
            </td>
            <td>
                <div>${exec.task_name || '--'}</div>
                <small class="text-muted">任务ID: ${exec.task_id}</small>
            </td>
            <td>
                <span class="status-badge status-${getStatusClass(exec.status)}">
                    <i class="fas fa-${getStatusIcon(exec.status)}"></i>
                    ${getStatusText(exec.status)}
                </span>
            </td>
            <td>${formatDateTime(exec.start_time)}</td>
            <td>${formatDateTime(exec.end_time)}</td>
            <td>${formatDuration(exec.duration)}</td>
            <td>
                <div>导出: ${exec.records_exported || 0}</div>
                <div>删除: ${exec.records_deleted || 0}</div>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="showExecutionDetail(${exec.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${exec.status === 'running' ? 
                        `<button class="btn btn-outline-warning" onclick="cancelExecution(${exec.id})" title="取消执行">
                            <i class="fas fa-stop"></i>
                        </button>` : ''
                    }
                    ${exec.file_path ? 
                        `<button class="btn btn-outline-success" onclick="downloadFile('${exec.file_path}')" title="下载文件">
                            <i class="fas fa-download"></i>
                        </button>` : ''
                    }
                </div>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// 渲染分页
function renderPagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    totalPages = pagination.pages;
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${pagination.page <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadExecutionHistory(${pagination.page - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(totalPages, pagination.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadExecutionHistory(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    html += `
        <li class="page-item ${pagination.page >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadExecutionHistory(${pagination.page + 1})">下一页</a>
        </li>
    `;
    
    paginationEl.innerHTML = html;
}

// 加载任务列表用于筛选
async function loadTasksForFilter() {
    try {
        const response = await axios.get('/api/archive-tasks');
        const tasks = response.data.data;
        
        const select = document.getElementById('task_filter');
        select.innerHTML = '<option value="">全部任务</option>';
        
        tasks.forEach(task => {
            const option = document.createElement('option');
            option.value = task.id;
            option.textContent = task.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载任务列表失败:', error);
    }
}

// 更新执行统计
async function updateExecutionStats() {
    try {
        const response = await axios.get('/api/dashboard/execution-stats');
        const stats = response.data.data;
        
        document.getElementById('total-executions').textContent = stats.total || 0;
        document.getElementById('success-executions').textContent = stats.success || 0;
        document.getElementById('failed-executions').textContent = stats.failed || 0;
        document.getElementById('running-executions').textContent = stats.running || 0;
    } catch (error) {
        console.error('更新执行统计失败:', error);
    }
}

// 应用筛选
function applyFilter() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    
    currentFilters = {};
    for (const [key, value] of formData.entries()) {
        if (value) {
            currentFilters[key] = value;
        }
    }
    
    loadExecutionHistory(1);
}

// 清除筛选
function clearFilter() {
    document.getElementById('filterForm').reset();
    currentFilters = {};
    loadExecutionHistory(1);
}

// 改变页面大小
function changePageSize(newSize) {
    pageSize = parseInt(newSize);
    loadExecutionHistory(1);
}

// 刷新历史
function refreshHistory() {
    loadExecutionHistory(currentPage);
}

// 显示执行详情
async function showExecutionDetail(id) {
    try {
        const response = await axios.get(`/api/task-executions/${id}`);
        const execution = response.data.data;
        
        currentExecutionId = id;
        
        const content = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>执行ID:</td><td>${execution.id}</td></tr>
                        <tr><td>任务名称:</td><td>${execution.task_name || '--'}</td></tr>
                        <tr><td>状态:</td><td>
                            <span class="status-badge status-${getStatusClass(execution.status)}">
                                ${getStatusText(execution.status)}
                            </span>
                        </td></tr>
                        <tr><td>开始时间:</td><td>${formatDateTime(execution.start_time)}</td></tr>
                        <tr><td>结束时间:</td><td>${formatDateTime(execution.end_time)}</td></tr>
                        <tr><td>持续时间:</td><td>${formatDuration(execution.duration)}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>执行结果</h6>
                    <table class="table table-sm">
                        <tr><td>导出记录数:</td><td>${execution.records_exported || 0}</td></tr>
                        <tr><td>删除记录数:</td><td>${execution.records_deleted || 0}</td></tr>
                        <tr><td>文件大小:</td><td>${formatFileSize(execution.file_size || 0)}</td></tr>
                        <tr><td>文件路径:</td><td>${execution.file_path || '--'}</td></tr>
                        <tr><td>压缩文件:</td><td>${execution.compressed_file_path || '--'}</td></tr>
                        <tr><td>上传状态:</td><td>${execution.upload_status || '--'}</td></tr>
                    </table>
                </div>
            </div>
            
            ${execution.error_message ? `
                <div class="mt-3">
                    <h6>错误信息</h6>
                    <div class="alert alert-danger">
                        <pre class="mb-0">${execution.error_message}</pre>
                    </div>
                </div>
            ` : ''}
            
            ${execution.logs ? `
                <div class="mt-3">
                    <h6>执行日志</h6>
                    <div class="bg-light p-3" style="max-height: 300px; overflow-y: auto;">
                        <pre class="mb-0">${execution.logs}</pre>
                    </div>
                </div>
            ` : ''}
        `;
        
        document.getElementById('executionDetailContent').innerHTML = content;
        
        const modal = new bootstrap.Modal(document.getElementById('executionDetailModal'));
        modal.show();
    } catch (error) {
        console.error('获取执行详情失败:', error);
        showMessage('获取执行详情失败: ' + error.message, 'error');
    }
}

// 取消执行
async function cancelExecution(id) {
    const confirmed = await confirmDialog('确定要取消这个正在执行的任务吗？');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.post(`/api/task-executions/${id}/cancel`);
        
        if (response.data.success) {
            showMessage('任务已取消', 'success');
            loadExecutionHistory(currentPage);
        } else {
            showMessage('取消任务失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('取消任务失败:', error);
        showMessage('取消任务失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 下载执行日志
function downloadExecutionLog() {
    if (currentExecutionId) {
        window.open(`/api/task-executions/${currentExecutionId}/download-log`, '_blank');
    }
}

// 下载文件
function downloadFile(filePath) {
    window.open(`/api/files/download?path=${encodeURIComponent(filePath)}`, '_blank');
}

// 导出历史
function exportHistory() {
    const params = new URLSearchParams(currentFilters);
    window.open(`/api/task-executions/export?${params}`, '_blank');
}

// 获取状态样式类
function getStatusClass(status) {
    const statusMap = {
        'running': 'warning',
        'success': 'success',
        'failed': 'error',
        'cancelled': 'secondary'
    };
    return statusMap[status] || 'info';
}

// 获取状态图标
function getStatusIcon(status) {
    const iconMap = {
        'running': 'spinner fa-spin',
        'success': 'check-circle',
        'failed': 'times-circle',
        'cancelled': 'ban'
    };
    return iconMap[status] || 'question-circle';
}

// 获取状态文本
function getStatusText(status) {
    const textMap = {
        'running': '运行中',
        'success': '成功',
        'failed': '失败',
        'cancelled': '已取消'
    };
    return textMap[status] || status;
}
</script>
{% endblock %}
