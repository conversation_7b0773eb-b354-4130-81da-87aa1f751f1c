import threading
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from flask import current_app
from app import db
from app.models import ArchiveTask, TaskExecution, SystemLog
from app.services.archive_service import ArchiveService
from app.services.notification_service import NotificationService

class SchedulerService:
    """任务调度服务类"""
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
        self._lock = threading.Lock()
        
    def start(self):
        """启动调度器"""
        with self._lock:
            if self.is_running:
                return
            
            try:
                self.scheduler = BackgroundScheduler(
                    timezone='Asia/Shanghai',
                    job_defaults={
                        'coalesce': True,
                        'max_instances': 1,
                        'misfire_grace_time': 300  # 5分钟的容错时间
                    }
                )
                
                # 添加事件监听器
                self.scheduler.add_listener(
                    self._job_executed_listener,
                    EVENT_JOB_EXECUTED | EVENT_JOB_ERROR
                )
                
                # 启动调度器
                self.scheduler.start()
                self.is_running = True
                
                # 加载所有活跃的定时任务
                self._load_scheduled_tasks()
                
                SystemLog.log_info('scheduler_service', '任务调度器启动成功')
                
            except Exception as e:
                SystemLog.log_error('scheduler_service', f'任务调度器启动失败: {str(e)}')
                raise
    
    def stop(self):
        """停止调度器"""
        with self._lock:
            if not self.is_running:
                return
            
            try:
                if self.scheduler:
                    self.scheduler.shutdown(wait=True)
                    self.scheduler = None
                
                self.is_running = False
                SystemLog.log_info('scheduler_service', '任务调度器已停止')
                
            except Exception as e:
                SystemLog.log_error('scheduler_service', f'任务调度器停止失败: {str(e)}')
    
    def _load_scheduled_tasks(self):
        """加载所有定时任务"""
        try:
            # 获取所有启用的定时任务
            tasks = ArchiveTask.query.filter_by(
                is_active=True,
                schedule_enabled=True
            ).all()
            
            for task in tasks:
                if task.schedule_cron:
                    self.add_scheduled_task(task)
            
            SystemLog.log_info('scheduler_service', f'加载了 {len(tasks)} 个定时任务')
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'加载定时任务失败: {str(e)}')
    
    def add_scheduled_task(self, task):
        """添加定时任务"""
        try:
            if not self.is_running:
                return False
            
            job_id = f"archive_task_{task.id}"
            
            # 如果任务已存在，先删除
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # 解析Cron表达式
            try:
                trigger = CronTrigger.from_crontab(task.schedule_cron)
            except ValueError as e:
                SystemLog.log_error('scheduler_service', f'无效的Cron表达式: {task.schedule_cron} - {str(e)}')
                return False
            
            # 添加任务
            self.scheduler.add_job(
                func=self._execute_archive_task,
                trigger=trigger,
                id=job_id,
                name=f"归档任务: {task.name}",
                args=[task.id],
                replace_existing=True
            )
            
            # 更新下次运行时间
            job = self.scheduler.get_job(job_id)
            if job and job.next_run_time:
                task.next_run_time = job.next_run_time
                db.session.commit()
            
            SystemLog.log_info('scheduler_service', f'添加定时任务: {task.name} - {task.schedule_cron}')
            return True
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'添加定时任务失败: {task.name} - {str(e)}')
            return False
    
    def remove_scheduled_task(self, task_id):
        """移除定时任务"""
        try:
            if not self.is_running:
                return False
            
            job_id = f"archive_task_{task_id}"
            
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                SystemLog.log_info('scheduler_service', f'移除定时任务: {task_id}')
                return True
            
            return False
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'移除定时任务失败: {task_id} - {str(e)}')
            return False
    
    def update_scheduled_task(self, task):
        """更新定时任务"""
        try:
            # 先移除旧任务
            self.remove_scheduled_task(task.id)
            
            # 如果任务启用且有调度配置，则添加新任务
            if task.is_active and task.schedule_enabled and task.schedule_cron:
                return self.add_scheduled_task(task)
            
            return True
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'更新定时任务失败: {task.name} - {str(e)}')
            return False
    
    def _execute_archive_task(self, task_id):
        """执行归档任务"""
        try:
            # 创建应用上下文
            with current_app.app_context():
                # 获取任务
                task = ArchiveTask.query.get(task_id)
                if not task:
                    SystemLog.log_error('scheduler_service', f'任务不存在: {task_id}')
                    return
                
                if not task.is_active:
                    SystemLog.log_warning('scheduler_service', f'任务已禁用: {task.name}')
                    return
                
                # 检查是否有正在运行的任务
                running_execution = TaskExecution.query.filter_by(
                    task_id=task_id,
                    status='running'
                ).first()
                
                if running_execution:
                    SystemLog.log_warning('scheduler_service', f'任务正在运行，跳过本次执行: {task.name}')
                    return
                
                # 更新最后运行时间
                task.last_run_time = datetime.utcnow()
                db.session.commit()
                
                # 执行归档任务
                archive_service = ArchiveService()
                execution = archive_service.execute_archive_task(task_id)
                
                # 发送通知
                notification_service = NotificationService()
                if execution.status == 'success':
                    notification_service.send_archive_success_notification(
                        task.name,
                        execution.id,
                        execution.records_exported,
                        execution.records_deleted,
                        execution.duration_formatted
                    )
                else:
                    notification_service.send_archive_failure_notification(
                        task.name,
                        execution.id,
                        execution.error_message
                    )
                
                SystemLog.log_info('scheduler_service', f'定时任务执行完成: {task.name} - {execution.status}')
                
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'定时任务执行异常: {task_id} - {str(e)}')
    
    def _job_executed_listener(self, event):
        """任务执行事件监听器"""
        try:
            job_id = event.job_id
            
            if event.exception:
                SystemLog.log_error('scheduler_service', f'定时任务执行失败: {job_id} - {str(event.exception)}')
            else:
                SystemLog.log_info('scheduler_service', f'定时任务执行成功: {job_id}')
                
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'任务事件监听器异常: {str(e)}')
    
    def get_job_status(self, task_id):
        """获取任务状态"""
        try:
            if not self.is_running:
                return None
            
            job_id = f"archive_task_{task_id}"
            job = self.scheduler.get_job(job_id)
            
            if job:
                return {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                }
            
            return None
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'获取任务状态失败: {task_id} - {str(e)}')
            return None
    
    def get_all_jobs(self):
        """获取所有任务状态"""
        try:
            if not self.is_running:
                return []
            
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            
            return jobs
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'获取所有任务状态失败: {str(e)}')
            return []
    
    def pause_job(self, task_id):
        """暂停任务"""
        try:
            if not self.is_running:
                return False
            
            job_id = f"archive_task_{task_id}"
            self.scheduler.pause_job(job_id)
            
            SystemLog.log_info('scheduler_service', f'暂停定时任务: {task_id}')
            return True
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'暂停定时任务失败: {task_id} - {str(e)}')
            return False
    
    def resume_job(self, task_id):
        """恢复任务"""
        try:
            if not self.is_running:
                return False
            
            job_id = f"archive_task_{task_id}"
            self.scheduler.resume_job(job_id)
            
            SystemLog.log_info('scheduler_service', f'恢复定时任务: {task_id}')
            return True
            
        except Exception as e:
            SystemLog.log_error('scheduler_service', f'恢复定时任务失败: {task_id} - {str(e)}')
            return False

# 全局调度器实例
scheduler_service = SchedulerService()
