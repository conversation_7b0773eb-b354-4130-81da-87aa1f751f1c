#!/bin/bash
#############################################################################
# Oracle Archive Agent 配置文件
# 将此文件复制为 agent.conf 并修改相应配置
#############################################################################

# Agent标识
AGENT_ID="oracle_agent_01"

# Oracle数据库配置
ORACLE_HOME="/u01/app/oracle/product/19.0.0/dbhome_1"
ORACLE_SID="ORCL"
DB_USER="archive_user"
DB_PASS="archive_password"

# 中央控制服务器
CENTRAL_SERVER="http://central-server.example.com:8080"

# 上传配置
UPLOAD_TYPE="scp"  # scp, sftp, http
UPLOAD_SERVER="backup-server.example.com"
UPLOAD_PATH="/backup/oracle_archives"
UPLOAD_USER="backup_user"

# 压缩配置
COMPRESSION_TYPE="gzip"  # gzip, zip, tar.gz
COMPRESSION_LEVEL="6"    # 1-9

# 归档配置
DELETE_AFTER_ARCHIVE="false"  # 是否在归档后删除原数据
BATCH_SIZE="10000"           # 批处理大小

# Agent配置
HEARTBEAT_INTERVAL="60"      # 心跳间隔（秒）
LOG_LEVEL="INFO"             # DEBUG, INFO, ERROR

# 性能配置
PARALLEL_JOBS="1"            # 并行任务数
MAX_RETRY="3"                # 最大重试次数
RETRY_DELAY="300"            # 重试延迟（秒）

# 通知配置（可选）
NOTIFY_ON_ERROR="true"
NOTIFY_EMAIL="<EMAIL>"
SMTP_SERVER="smtp.example.com"
SMTP_PORT="25" 