#!/usr/bin/env python3
"""
OpenList认证调试工具
用于查看详细的登录响应，帮助用户了解token格式
"""

import asyncio
import aiohttp
import yaml
import json
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from strm import load_config


async def debug_auth(config_path: str):
    """调试OpenList认证"""
    print("=" * 60)
    print("OpenList 认证调试工具")
    print("=" * 60)
    
    try:
        # 加载配置
        config = load_config(config_path)
        print(f"✓ 配置文件加载成功: {config_path}")
        
        # 显示配置信息
        base_url = config['openlist']['base_url']
        username = config['openlist'].get('username', '')
        password = config['openlist'].get('password', '')
        token = config['openlist'].get('token', '')
        
        print(f"\n服务器地址: {base_url}")
        print(f"用户名: {username if username else '(未设置)'}")
        print(f"密码: {'***' if password else '(未设置)'}")
        print(f"Token: {token[:20] + '...' if token else '(未设置)'}")
        
        # 创建HTTP客户端
        connector = aiohttp.TCPConnector(ssl=False)
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        ) as session:
            
            # 如果有token，测试直接使用token
            if token:
                print(f"\n=== 测试Token认证 ===")
                await test_with_token(session, base_url, token)
            
            # 如果有用户名密码，测试登录
            if username and password:
                print(f"\n=== 测试用户名密码登录 ===")
                await test_login(session, base_url, username, password)
            
            # 测试匿名访问
            print(f"\n=== 测试匿名访问 ===")
            await test_anonymous(session, base_url)
                
    except FileNotFoundError:
        print(f"✗ 配置文件不存在: {config_path}")
    except KeyError as e:
        print(f"✗ 配置文件缺少必要字段: {e}")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        
    print("=" * 60)


async def test_with_token(session: aiohttp.ClientSession, base_url: str, token: str):
    """测试使用token进行认证"""
    test_url = f"{base_url}/api/fs/list"
    headers = {"Authorization": token}
    data = {"path": "/", "page": 1, "per_page": 10}
    
    try:
        print(f"请求URL: {test_url}")
        print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        async with session.post(test_url, json=data, headers=headers) as resp:
            print(f"\n响应状态: {resp.status}")
            print(f"响应头: {dict(resp.headers)}")
            
            if resp.content_type == 'application/json':
                response_data = await resp.json()
                print(f"响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                if resp.status == 200:
                    print("✓ Token认证成功！")
                else:
                    print("✗ Token认证失败")
            else:
                text = await resp.text()
                print(f"响应内容 (文本): {text[:500]}")
                
    except Exception as e:
        print(f"✗ Token测试出错: {e}")


async def test_login(session: aiohttp.ClientSession, base_url: str, username: str, password: str):
    """测试用户名密码登录"""
    login_url = f"{base_url}/api/auth/login"
    login_data = {"username": username, "password": password}
    
    try:
        print(f"请求URL: {login_url}")
        print(f"请求数据: {json.dumps(login_data, indent=2, ensure_ascii=False)}")
        
        async with session.post(login_url, json=login_data) as resp:
            print(f"\n响应状态: {resp.status}")
            print(f"响应头: {dict(resp.headers)}")
            
            if resp.content_type == 'application/json':
                response_data = await resp.json()
                print(f"响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                if resp.status == 200:
                    print("✓ 登录请求成功！")
                    
                    # 分析token字段
                    print("\n=== Token字段分析 ===")
                    token_fields = [
                        ('data', 'token'),
                        ('token',),
                        ('access_token',),
                        ('data', 'access_token'),
                        ('result', 'token'),
                        ('result', 'access_token'),
                    ]
                    
                    found_token = False
                    for fields in token_fields:
                        try:
                            current = response_data
                            for field in fields:
                                current = current[field]
                            if current:
                                print(f"✓ 找到token在: {' -> '.join(fields)}")
                                print(f"  Token值: {current[:20]}...")
                                found_token = True
                                break
                        except (KeyError, TypeError):
                            continue
                    
                    if not found_token:
                        print("✗ 未找到任何token字段")
                        print("请检查响应内容，可能需要修改代码以支持此格式")
                        
                else:
                    print("✗ 登录失败")
            else:
                text = await resp.text()
                print(f"响应内容 (文本): {text[:500]}")
                
    except Exception as e:
        print(f"✗ 登录测试出错: {e}")


async def test_anonymous(session: aiohttp.ClientSession, base_url: str):
    """测试匿名访问"""
    test_url = f"{base_url}/api/fs/list"
    data = {"path": "/", "page": 1, "per_page": 10}
    
    try:
        print(f"请求URL: {test_url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        async with session.post(test_url, json=data) as resp:
            print(f"\n响应状态: {resp.status}")
            print(f"响应头: {dict(resp.headers)}")
            
            if resp.content_type == 'application/json':
                response_data = await resp.json()
                print(f"响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)[:500]}")
                
                if resp.status == 200:
                    print("✓ 匿名访问成功！")
                else:
                    print("✗ 匿名访问失败")
            else:
                text = await resp.text()
                print(f"响应内容 (文本): {text[:500]}")
                
    except Exception as e:
        print(f"✗ 匿名访问测试出错: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OpenList认证调试工具')
    parser.add_argument(
        '-c', '--config',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    args = parser.parse_args()
    
    # 运行调试
    asyncio.run(debug_auth(args.config))


if __name__ == '__main__':
    main() 