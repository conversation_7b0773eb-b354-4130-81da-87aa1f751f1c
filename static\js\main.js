// 数据库归档管理平台 - 通用JavaScript功能

// 全局变量
window.APP = {
    baseURL: '',
    currentUser: null,
    config: {}
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 设置axios默认配置
    axios.defaults.timeout = 30000;
    axios.defaults.headers.common['Content-Type'] = 'application/json';
    
    // 设置axios拦截器
    setupAxiosInterceptors();
    
    // 初始化工具提示
    initializeTooltips();
    
    console.log('数据库归档管理平台已初始化');
}

// 设置axios拦截器
function setupAxiosInterceptors() {
    // 请求拦截器
    axios.interceptors.request.use(
        function(config) {
            return config;
        },
        function(error) {
            return Promise.reject(error);
        }
    );
    
    // 响应拦截器
    axios.interceptors.response.use(
        function(response) {
            return response;
        },
        function(error) {
            // 处理常见错误
            if (error.response) {
                const status = error.response.status;
                const message = error.response.data?.message || error.message;
                
                switch (status) {
                    case 401:
                        showMessage('未授权访问，请重新登录', 'error');
                        break;
                    case 403:
                        showMessage('权限不足', 'error');
                        break;
                    case 404:
                        showMessage('请求的资源不存在', 'error');
                        break;
                    case 500:
                        showMessage('服务器内部错误', 'error');
                        break;
                    default:
                        showMessage(message, 'error');
                }
            } else if (error.request) {
                showMessage('网络连接失败，请检查网络设置', 'error');
            } else {
                showMessage('请求失败: ' + error.message, 'error');
            }
            
            return Promise.reject(error);
        }
    );
}

// 初始化工具提示
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 显示加载遮罩
function showLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.remove('d-none');
    }
}

// 隐藏加载遮罩
function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.add('d-none');
    }
}

// 显示消息提示
function showMessage(message, type = 'info', duration = 5000) {
    const container = document.getElementById('message-container');
    if (!container) {
        // 如果没有消息容器，使用alert
        alert(message);
        return;
    }
    
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const icon = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert ${alertClass} alert-dismissible fade show`;
    alertElement.innerHTML = `
        <i class="${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    container.appendChild(alertElement);
    
    // 自动移除消息
    if (duration > 0) {
        setTimeout(() => {
            if (alertElement.parentNode) {
                alertElement.remove();
            }
        }, duration);
    }
}

// 确认对话框
function confirmDialog(message, title = '确认操作') {
    return new Promise((resolve) => {
        const result = confirm(`${title}\n\n${message}`);
        resolve(result);
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化持续时间
function formatDuration(seconds) {
    if (!seconds || seconds < 0) return '--';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}小时${minutes}分钟${secs}秒`;
    } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
    } else {
        return `${secs}秒`;
    }
}

// 格式化日期时间
function formatDateTime(dateString, format = 'full') {
    if (!dateString) return '--';
    
    const date = new Date(dateString);
    
    switch (format) {
        case 'date':
            return date.toLocaleDateString('zh-CN');
        case 'time':
            return date.toLocaleTimeString('zh-CN');
        case 'short':
            return date.toLocaleString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        default:
            return date.toLocaleString('zh-CN');
    }
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showMessage('已复制到剪贴板', 'success', 2000);
    } catch (error) {
        console.error('复制失败:', error);
        showMessage('复制失败', 'error');
    }
}

// 验证表单
function validateForm(formElement) {
    const requiredFields = formElement.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// 清除表单验证状态
function clearFormValidation(formElement) {
    const fields = formElement.querySelectorAll('.is-invalid, .is-valid');
    fields.forEach(field => {
        field.classList.remove('is-invalid', 'is-valid');
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 导出功能
window.APP.utils = {
    showLoading,
    hideLoading,
    showMessage,
    confirmDialog,
    formatFileSize,
    formatDuration,
    formatDateTime,
    copyToClipboard,
    validateForm,
    clearFormValidation,
    debounce
};
