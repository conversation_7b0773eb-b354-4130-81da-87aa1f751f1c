# OpenList STRM 下载工具

一个用于从 OpenList (类似 AList) 服务器下载视频目录结构并生成 STRM 文件的异步工具。支持下载 NFO 元数据和图片文件，适用于 Kodi、Emby、Plex 等媒体服务器。

## 功能特点

- 🚀 **异步并发下载** - 高效的异步处理，支持并发下载
- 📁 **递归目录扫描** - 自动扫描子目录，支持深度限制
- 🎬 **STRM 文件生成** - 为视频文件生成流媒体链接文件
- 🖼️ **元数据支持** - 自动下载 NFO 文件和图片（海报、封面等）
- 🔧 **灵活配置** - 支持多种配置选项和过滤器
- 🔄 **错误重试** - 自动重试失败的请求
- 📊 **进度统计** - 实时显示处理进度和统计信息

## 安装

1. 克隆或下载项目
2. 安装依赖：

```bash
pip install -r requirements.txt
```

## 配置

复制 `config.yaml` 文件并根据需要修改：

```yaml
# OpenList服务器配置
openlist:
  base_url: "https://your-openlist-server.com"
  username: "your_username" # 可选
  password: "your_password" # 可选
  source_path: "/movies" # 要扫描的源目录

# 本地存储配置
local:
  base_path: "./downloads" # STRM文件保存位置

# 其他配置项见 config.yaml
```

### 主要配置项说明

- **openlist.base_url**: OpenList 服务器地址
- **openlist.source_path**: 要扫描的源目录路径
- **local.base_path**: 本地保存 STRM 文件的目录
- **max_depth**: 最大目录深度（防止递归过深）
- **concurrent_downloads**: 并发下载数
- **download_metadata**: 是否下载 NFO 和图片文件

### STRM 文件 URL 格式配置

```yaml
strm:
  # URL格式设置
  url_format: "custom" # 可选: "full", "relative", "custom"

  # 自定义URL前缀
  custom_prefix: "http://***********:5244"

  # 是否对URL进行编码
  url_encode: false
```

**url_format 选项说明：**

- `full`: 使用 OpenList 返回的完整 URL（默认）
- `relative`: 只保存相对路径，如 `/123/EMBY/movie.mkv`
- `custom`: 使用自定义前缀+相对路径，如 `http://***********:5244/123/EMBY/movie.mkv`

## 使用方法

### 基本使用

```bash
python strm.py
```

### 指定配置文件

```bash
python strm.py -c custom_config.yaml
```

### 命令行参数

- `-c, --config`: 指定配置文件路径（默认: config.yaml）

## 工作原理

1. **认证**: 如果配置了用户名密码，首先进行登录认证
2. **目录扫描**: 递归扫描指定的源目录
3. **文件处理**:
   - 视频文件 → 生成包含直链的 STRM 文件
   - NFO 文件 → 下载到本地对应位置
   - 图片文件 → 下载到本地对应位置
4. **本地结构**: 完全保持源目录的文件夹结构

## 支持的文件格式

### 视频格式（生成 STRM）

- `.mp4`, `.mkv`, `.avi`, `.mov`, `.wmv`, `.flv`
- `.webm`, `.m4v`, `.mpg`, `.mpeg`, `.3gp`, `.rmvb`
- `.ts`, `.m2ts`, `.vob`, `.asf`, `.rm`, `.m3u8`

### 元数据格式（下载）

- NFO 文件: `.nfo`
- 图片文件: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.webp`等

## 高级功能

### 文件过滤

在配置文件中设置过滤器：

```yaml
filter:
  # 自定义视频格式
  video_extensions:
    - ".mp4"
    - ".mkv"

  # 排除模式（正则表达式）
  exclude_patterns:
    - ".*\\.tmp$"
    - ".*sample.*"

  # 文件大小限制
  min_file_size: 1048576 # 1MB
  max_file_size: 5368709120 # 5GB
```

### 代理设置

```yaml
advanced:
  proxy: "http://127.0.0.1:7890"
  verify_ssl: false
```

## 日志

程序会生成两种日志：

- 控制台输出：实时显示处理进度
- 文件日志：`strm_downloader.log`

## 常见问题

### Q: STRM 文件是什么？

A: STRM 文件是包含视频 URL 的文本文件，媒体服务器可以通过它直接播放网络视频。

### Q: 为什么要使用 STRM 而不是直接下载视频？

A: STRM 文件体积极小，可以快速创建整个媒体库的索引，按需播放时才消耗带宽。

### Q: 支持哪些 OpenList 版本？

A: 支持标准 OpenList API 的所有版本。

## 注意事项

1. 确保 OpenList 服务器可访问
2. 大量文件时建议适当设置并发数
3. 首次运行可能需要较长时间，后续运行会跳过已存在的文件
4. 建议定期运行以同步新增内容

## License

MIT License
