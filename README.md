# 数据库归档管理平台

基于Python Flask的Oracle数据库归档管理解决方案，提供Web界面管理、定时任务调度、多种通知方式和文件传输功能。

## 功能特性

### 核心功能
- 🗄️ **Oracle数据库归档**: 支持多个Oracle数据库实例连接
- 📅 **灵活的归档策略**: 可配置归档条件、时间范围和自定义WHERE条件
- 🗜️ **数据压缩**: 支持gzip压缩，可配置压缩级别
- 🔄 **批量删除**: 分批删除避免长时间锁表
- 📊 **统计信息更新**: 自动更新表统计信息

### 管理界面
- 🌐 **Web管理界面**: 基于Vue.js + Element UI的现代化界面
- 📈 **仪表板**: 实时监控任务状态和系统健康
- ⚙️ **配置管理**: 可视化配置数据库连接和归档任务
- 📋 **执行历史**: 详细的任务执行记录和日志

### 调度和监控
- ⏰ **定时任务**: 基于Cron表达式的灵活调度
- 📊 **实时监控**: 任务状态、执行进度和性能指标
- 📝 **详细日志**: 完整的操作日志和错误追踪
- 🔍 **执行统计**: 成功率、执行时间等统计分析

### 文件传输
- 📤 **SFTP上传**: 支持SFTP协议文件传输
- 📤 **FTP上传**: 支持FTP协议文件传输
- 💾 **本地存储**: 本地文件系统存储
- 🔄 **重试机制**: 上传失败自动重试

### 通知系统
- 📧 **邮件通知**: SMTP邮件通知支持
- 💬 **企业微信**: 企业微信机器人通知
- 📱 **钉钉通知**: 钉钉机器人通知
- 🎯 **灵活规则**: 可配置成功/失败/警告通知规则

## 系统要求

- Python 3.8+
- Oracle数据库 (支持oracledb驱动)
- 操作系统: Windows/Linux/macOS

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd archive_platform
```

### 2. 运行安装脚本
```bash
python install.py
```

安装脚本会自动：
- 检查Python版本
- 安装依赖包
- 创建必要目录
- 生成安全密钥
- 初始化数据库
- 创建示例数据

### 3. 配置系统
编辑 `config/archive_config.yaml` 文件：

```yaml
# 数据库配置
database:
  url: "sqlite:///data/archive_platform.db"

# 通知配置
notification:
  email:
    enabled: true
    smtp_server: "your-smtp-server"
    smtp_port: 587
    username: "your-email"
    password: "your-password"
    from_address: "your-email"

# 上传配置
upload:
  sftp:
    host: "your-sftp-server"
    port: 22
    username: "your-username"
    password: "your-password"
    remote_path: "/backup"
```

### 4. 启动应用
```bash
python run.py
```

### 5. 访问系统
打开浏览器访问: http://localhost:8080

## 使用指南

### 1. 配置数据库连接
1. 进入"数据库连接管理"页面
2. 点击"新增连接"
3. 填写Oracle数据库连接信息
4. 测试连接确保配置正确

### 2. 创建归档任务
1. 进入"归档任务管理"页面
2. 点击"新建任务"
3. 配置任务参数：
   - 选择数据库连接
   - 指定表名和日期字段
   - 设置归档条件
   - 配置压缩和删除选项
   - 设置定时调度

### 3. 配置文件上传
1. 在任务配置中添加上传配置
2. 选择上传类型（SFTP/FTP/本地）
3. 配置服务器信息和路径
4. 测试连接确保配置正确

### 4. 设置通知
1. 进入"系统设置"页面
2. 配置邮件服务器信息
3. 添加企业微信或钉钉Webhook
4. 设置通知规则

## 项目结构

```
archive_platform/
├── app/                    # 应用主目录
│   ├── __init__.py        # Flask应用工厂
│   ├── models/            # 数据模型
│   ├── api/               # API路由
│   ├── services/          # 业务服务
│   └── utils/             # 工具函数
├── config/                # 配置文件
├── templates/             # 前端模板
├── static/                # 静态文件
├── data/                  # 数据文件
├── logs/                  # 日志文件
├── temp/                  # 临时文件
├── backups/               # 备份文件
├── requirements.txt       # Python依赖
├── run.py                 # 启动脚本
├── install.py             # 安装脚本
└── README.md              # 说明文档
```

## API文档

### 数据库连接
- `GET /api/database-connections` - 获取连接列表
- `POST /api/database-connections` - 创建连接
- `PUT /api/database-connections/{id}` - 更新连接
- `DELETE /api/database-connections/{id}` - 删除连接
- `POST /api/database-connections/{id}/test` - 测试连接

### 归档任务
- `GET /api/archive-tasks` - 获取任务列表
- `POST /api/archive-tasks` - 创建任务
- `PUT /api/archive-tasks/{id}` - 更新任务
- `DELETE /api/archive-tasks/{id}` - 删除任务
- `POST /api/archive-tasks/{id}/execute` - 执行任务

### 执行历史
- `GET /api/task-executions` - 获取执行记录
- `GET /api/task-executions/{id}` - 获取执行详情
- `GET /api/task-executions/{id}/log` - 获取执行日志

## 部署指南

### 开发环境
```bash
python run.py
```

### 生产环境
推荐使用Docker部署：

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8080
CMD ["python", "run.py"]
```

## 安全注意事项

1. **密钥管理**: 生产环境请修改配置文件中的默认密钥
2. **数据库密码**: 所有数据库密码都经过加密存储
3. **访问控制**: 建议在生产环境中配置防火墙和访问控制
4. **HTTPS**: 生产环境建议启用HTTPS

## 故障排除

### 常见问题

1. **Oracle连接失败**
   - 检查Oracle客户端是否正确安装
   - 验证网络连接和防火墙设置
   - 确认数据库服务名和端口

2. **任务执行失败**
   - 查看执行日志获取详细错误信息
   - 检查数据库权限和表结构
   - 验证归档条件SQL语法

3. **文件上传失败**
   - 测试SFTP/FTP连接配置
   - 检查远程服务器权限和路径
   - 查看网络连接状态

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 技术支持
