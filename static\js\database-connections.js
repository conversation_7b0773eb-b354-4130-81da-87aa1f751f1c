// 数据库连接管理JavaScript

// 全局变量
let currentConnection = null;
let connections = [];

// 保存连接
async function saveConnection() {
    const form = document.getElementById('connectionForm');

    if (!validateForm(form)) {
        showMessage('请填写所有必填字段', 'error');
        return;
    }

    const formData = new FormData(form);
    const data = {
        name: formData.get('name'),
        db_type: formData.get('db_type'),
        host: formData.get('host'),
        port: parseInt(formData.get('port')),
        database: formData.get('database'),
        username: formData.get('username'),
        password: formData.get('password'),
        is_active: formData.has('is_active')
    };

    try {
        showLoading();
        let response;

        if (form.dataset.connectionId) {
            // 更新连接
            response = await axios.put(`/api/database-connections/${form.dataset.connectionId}`, data);
        } else {
            // 创建连接
            response = await axios.post('/api/database-connections', data);
        }

        if (response.data.success) {
            showMessage(response.data.message, 'success');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('connectionModal'));
            modal.hide();

            // 重新加载连接列表
            loadConnections();
        } else {
            showMessage(response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存连接失败:', error);
        showMessage('保存连接失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 测试连接
async function testConnection() {
    const form = document.getElementById('connectionForm');

    if (!validateForm(form)) {
        showMessage('请填写连接信息', 'error');
        return;
    }

    const formData = new FormData(form);
    const data = {
        name: formData.get('name') || 'test',
        db_type: formData.get('db_type'),
        host: formData.get('host'),
        port: parseInt(formData.get('port')),
        database: formData.get('database'),
        username: formData.get('username'),
        password: formData.get('password'),
        is_active: true
    };

    try {
        showLoading();

        // 创建临时连接进行测试
        const response = await axios.post('/api/database-connections', {
            ...data,
            name: 'temp_test_' + Date.now()
        });

        if (response.data.success) {
            // 测试连接
            const testResponse = await axios.post(`/api/database-connections/${response.data.data.id}/test`);

            // 删除临时连接
            await axios.delete(`/api/database-connections/${response.data.data.id}`);

            if (testResponse.data.success) {
                showMessage('连接测试成功: ' + testResponse.data.message, 'success');
            } else {
                showMessage('连接测试失败: ' + testResponse.data.message, 'error');
            }
        } else {
            showMessage('连接测试失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('连接测试失败:', error);
        showMessage('连接测试失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 通过ID测试连接
async function testConnectionById(id) {
    try {
        showLoading();
        const response = await axios.post(`/api/database-connections/${id}/test`);

        if (response.data.success) {
            showMessage('连接测试成功: ' + response.data.message, 'success');
        } else {
            showMessage('连接测试失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('连接测试失败:', error);
        showMessage('连接测试失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 编辑连接
async function editConnection(id) {
    try {
        const response = await axios.get(`/api/database-connections/${id}`);
        const connection = response.data.data;

        showConnectionDialog(connection);
    } catch (error) {
        console.error('获取连接信息失败:', error);
        showMessage('获取连接信息失败: ' + error.message, 'error');
    }
}

// 删除连接
async function deleteConnection(id) {
    const confirmed = await confirmDialog('确定要删除这个数据库连接吗？此操作不可撤销。');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.delete(`/api/database-connections/${id}`);

        if (response.data.success) {
            showMessage(response.data.message, 'success');
            loadConnections();
        } else {
            showMessage(response.data.message, 'error');
        }
    } catch (error) {
        console.error('删除连接失败:', error);
        showMessage('删除连接失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 显示连接详情
async function showConnectionDetail(id) {
    try {
        const response = await axios.get(`/api/database-connections/${id}`);
        const connection = response.data.data;

        const content = `
            <div class="row">
                <div class="col-sm-4"><strong>连接名称:</strong></div>
                <div class="col-sm-8">${connection.name}</div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-4"><strong>数据库类型:</strong></div>
                <div class="col-sm-8"><span class="badge bg-primary">${connection.db_type.toUpperCase()}</span></div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-4"><strong>主机地址:</strong></div>
                <div class="col-sm-8">${connection.host}:${connection.port}</div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-4"><strong>数据库:</strong></div>
                <div class="col-sm-8">${connection.database}</div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-4"><strong>用户名:</strong></div>
                <div class="col-sm-8">${connection.username}</div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-4"><strong>状态:</strong></div>
                <div class="col-sm-8">
                    <span class="status-badge status-${connection.is_active ? 'success' : 'error'}">
                        <i class="fas fa-${connection.is_active ? 'check' : 'times'}-circle"></i>
                        ${connection.is_active ? '启用' : '禁用'}
                    </span>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-4"><strong>创建时间:</strong></div>
                <div class="col-sm-8">${formatDateTime(connection.created_at)}</div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-4"><strong>更新时间:</strong></div>
                <div class="col-sm-8">${formatDateTime(connection.updated_at)}</div>
            </div>
        `;

        document.getElementById('connectionDetailContent').innerHTML = content;

        const modal = new bootstrap.Modal(document.getElementById('connectionDetailModal'));
        modal.show();
    } catch (error) {
        console.error('获取连接详情失败:', error);
        showMessage('获取连接详情失败: ' + error.message, 'error');
    }
}

    renderConnectionsList() {
        const container = document.getElementById('connectionsContainer');
        if (!container) return;

        if (this.connections.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>暂无数据库连接</p>
                    <button class="btn btn-primary" onclick="dbManager.showConnectionDialog()">
                        添加第一个连接
                    </button>
                </div>
            `;
            return;
        }

        const html = this.connections.map(conn => `
            <div class="connection-card" data-id="${conn.id}">
                <div class="connection-header">
                    <h3>${conn.name}</h3>
                    <div class="connection-status ${conn.is_active ? 'active' : 'inactive'}">
                        ${conn.is_active ? '启用' : '禁用'}
                    </div>
                </div>
                <div class="connection-info">
                    <p><strong>类型:</strong> ${conn.db_type}</p>
                    <p><strong>主机:</strong> ${conn.host}:${conn.port}</p>
                    <p><strong>数据库:</strong> ${conn.database}</p>
                    <p><strong>用户名:</strong> ${conn.username}</p>
                    <p><strong>创建时间:</strong> ${this.formatDate(conn.created_at)}</p>
                </div>
                <div class="connection-actions">
                    <button class="btn btn-sm btn-primary" onclick="dbManager.testConnectionById(${conn.id})">
                        测试连接
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="dbManager.editConnection(${conn.id})">
                        编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="dbManager.deleteConnection(${conn.id})">
                        删除
                    </button>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    showConnectionDialog(connection = null) {
        this.currentConnection = connection;
        
        const dialog = document.getElementById('connectionDialog');
        const form = document.getElementById('connectionForm');
        
        if (connection) {
            // 编辑模式
            document.getElementById('dialogTitle').textContent = '编辑数据库连接';
            form.name.value = connection.name;
            form.db_type.value = connection.db_type;
            form.host.value = connection.host;
            form.port.value = connection.port;
            form.database.value = connection.database;
            form.username.value = connection.username;
            form.password.value = '';
            form.is_active.checked = connection.is_active;
        } else {
            // 新增模式
            document.getElementById('dialogTitle').textContent = '添加数据库连接';
            form.reset();
            form.db_type.value = 'oracle';
            form.port.value = '1521';
            form.is_active.checked = true;
        }
        
        dialog.style.display = 'block';
    }

    hideConnectionDialog() {
        document.getElementById('connectionDialog').style.display = 'none';
        this.currentConnection = null;
    }

    async saveConnection() {
        const form = document.getElementById('connectionForm');
        const formData = new FormData(form);
        
        const data = {
            name: formData.get('name'),
            db_type: formData.get('db_type'),
            host: formData.get('host'),
            port: parseInt(formData.get('port')),
            database: formData.get('database'),
            username: formData.get('username'),
            password: formData.get('password'),
            is_active: formData.has('is_active')
        };

        // 验证必填字段
        if (!data.name || !data.host || !data.database || !data.username) {
            this.showMessage('请填写所有必填字段', 'error');
            return;
        }

        try {
            let response;
            if (this.currentConnection) {
                // 更新连接
                response = await fetch(`/api/database-connections/${this.currentConnection.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
            } else {
                // 创建连接
                response = await fetch('/api/database-connections', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
            }

            const result = await response.json();
            
            if (result.success) {
                this.showMessage(result.message, 'success');
                this.hideConnectionDialog();
                this.loadConnections();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            this.showMessage('保存连接失败: ' + error.message, 'error');
        }
    }

    async testConnectionById(id) {
        try {
            const response = await fetch(`/api/database-connections/${id}/test`, {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('连接测试成功: ' + result.message, 'success');
            } else {
                this.showMessage('连接测试失败: ' + result.message, 'error');
            }
        } catch (error) {
            this.showMessage('连接测试失败: ' + error.message, 'error');
        }
    }

    async testConnection() {
        const form = document.getElementById('connectionForm');
        const formData = new FormData(form);
        
        const data = {
            name: formData.get('name') || 'test',
            db_type: formData.get('db_type'),
            host: formData.get('host'),
            port: parseInt(formData.get('port')),
            database: formData.get('database'),
            username: formData.get('username'),
            password: formData.get('password'),
            is_active: true
        };

        if (!data.host || !data.database || !data.username || !data.password) {
            this.showMessage('请填写连接信息', 'error');
            return;
        }

        try {
            // 创建临时连接进行测试
            const response = await fetch('/api/database-connections', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({...data, name: 'temp_test_' + Date.now()})
            });

            const result = await response.json();
            
            if (result.success) {
                // 测试连接
                const testResponse = await fetch(`/api/database-connections/${result.data.id}/test`, {
                    method: 'POST'
                });
                const testResult = await testResponse.json();
                
                // 删除临时连接
                await fetch(`/api/database-connections/${result.data.id}`, {
                    method: 'DELETE'
                });
                
                if (testResult.success) {
                    this.showMessage('连接测试成功: ' + testResult.message, 'success');
                } else {
                    this.showMessage('连接测试失败: ' + testResult.message, 'error');
                }
            } else {
                this.showMessage('连接测试失败: ' + result.message, 'error');
            }
        } catch (error) {
            this.showMessage('连接测试失败: ' + error.message, 'error');
        }
    }

    editConnection(id) {
        const connection = this.connections.find(c => c.id === id);
        if (connection) {
            this.showConnectionDialog(connection);
        }
    }

    async deleteConnection(id) {
        if (!confirm('确定要删除这个数据库连接吗？')) {
            return;
        }

        try {
            const response = await fetch(`/api/database-connections/${id}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            
            if (result.success) {
                this.showMessage(result.message, 'success');
                this.loadConnections();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            this.showMessage('删除连接失败: ' + error.message, 'error');
        }
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        
        // 添加到页面
        document.body.appendChild(messageDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }
}

// 初始化数据库连接管理器
let dbManager;
document.addEventListener('DOMContentLoaded', () => {
    dbManager = new DatabaseConnectionManager();
});
