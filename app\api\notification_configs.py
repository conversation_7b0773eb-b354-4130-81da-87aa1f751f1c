from flask import request, jsonify, current_app
from app import db
from app.models import NotificationConfig, SystemLog
from app.api import api_bp

@api_bp.route('/notification-configs', methods=['GET'])
def get_notification_configs():
    """获取通知配置列表"""
    try:
        configs = NotificationConfig.query.all()
        return jsonify({
            'success': True,
            'data': [config.to_dict() for config in configs]
        })
    except Exception as e:
        SystemLog.log_error('notification_configs', f'获取通知配置列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取通知配置列表失败: {str(e)}'
        }), 500

@api_bp.route('/notification-configs', methods=['POST'])
def create_notification_config():
    """创建通知配置"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['type', 'name', 'config']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 创建通知配置
        config = NotificationConfig(
            type=data['type'],
            name=data['name'],
            notify_on_success=data.get('notify_on_success', True),
            notify_on_failure=data.get('notify_on_failure', True),
            notify_on_warning=data.get('notify_on_warning', True),
            is_active=data.get('is_active', True)
        )
        
        config.set_config(data['config'])
        
        db.session.add(config)
        db.session.commit()
        
        SystemLog.log_info('notification_configs', f'创建通知配置: {config.name}')
        
        return jsonify({
            'success': True,
            'message': '通知配置创建成功',
            'data': config.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('notification_configs', f'创建通知配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'创建通知配置失败: {str(e)}'
        }), 500

@api_bp.route('/notification-configs/<int:config_id>', methods=['PUT'])
def update_notification_config(config_id):
    """更新通知配置"""
    try:
        config = NotificationConfig.query.get_or_404(config_id)
        data = request.get_json()

        # 更新字段
        if 'name' in data:
            config.name = data['name']
        if 'config' in data:
            config.set_config(data['config'])
        if 'notify_on_success' in data:
            config.notify_on_success = data['notify_on_success']
        if 'notify_on_failure' in data:
            config.notify_on_failure = data['notify_on_failure']
        if 'notify_on_warning' in data:
            config.notify_on_warning = data['notify_on_warning']
        if 'is_active' in data:
            config.is_active = data['is_active']

        db.session.commit()

        SystemLog.log_info('notification_configs', f'更新通知配置: {config.name}')

        return jsonify({
            'success': True,
            'message': '通知配置更新成功',
            'data': config.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('notification_configs', f'更新通知配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'更新通知配置失败: {str(e)}'
        }), 500

@api_bp.route('/notification-configs/<int:config_id>', methods=['DELETE'])
def delete_notification_config(config_id):
    """删除通知配置"""
    try:
        config = NotificationConfig.query.get_or_404(config_id)

        config_name = config.name
        db.session.delete(config)
        db.session.commit()

        SystemLog.log_info('notification_configs', f'删除通知配置: {config_name}')

        return jsonify({
            'success': True,
            'message': '通知配置删除成功'
        })

    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('notification_configs', f'删除通知配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除通知配置失败: {str(e)}'
        }), 500

@api_bp.route('/notification-configs/<int:config_id>/test', methods=['POST'])
def test_notification_config(config_id):
    """测试通知配置"""
    try:
        config = NotificationConfig.query.get_or_404(config_id)

        success, message = config.test_notification()

        SystemLog.log_info('notification_configs', f'测试通知配置: {config.name} - {message}')

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        SystemLog.log_error('notification_configs', f'测试通知配置失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'测试通知配置失败: {str(e)}'
        }), 500

@api_bp.route('/notification-configs/<int:config_id>/send-test', methods=['POST'])
def send_test_notification(config_id):
    """发送测试通知"""
    try:
        config = NotificationConfig.query.get_or_404(config_id)
        data = request.get_json()
        message = data.get('message', '这是一条测试通知消息')

        # 这里应该调用通知服务发送实际的测试消息
        # 暂时返回成功

        SystemLog.log_info('notification_configs', f'发送测试通知: {config.name}')

        return jsonify({
            'success': True,
            'message': '测试通知发送成功'
        })

    except Exception as e:
        SystemLog.log_error('notification_configs', f'发送测试通知失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'发送测试通知失败: {str(e)}'
        }), 500
