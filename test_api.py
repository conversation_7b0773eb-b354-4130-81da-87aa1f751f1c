#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8080/api"

def test_database_connections():
    """测试数据库连接API"""
    print("=== 测试数据库连接API ===")
    
    # 获取连接列表
    response = requests.get(f"{BASE_URL}/database-connections")
    print(f"获取连接列表: {response.status_code}")
    print(f"响应: {response.json()}")
    
    # 创建新连接
    new_connection = {
        "name": "测试Oracle连接",
        "db_type": "oracle",
        "host": "localhost",
        "port": 1521,
        "database": "XE",
        "username": "test_user",
        "password": "test_password",
        "is_active": True
    }
    
    response = requests.post(f"{BASE_URL}/database-connections", json=new_connection)
    print(f"创建连接: {response.status_code}")
    print(f"响应: {response.json()}")
    
    if response.status_code == 200:
        connection_id = response.json()['data']['id']
        
        # 测试连接
        response = requests.post(f"{BASE_URL}/database-connections/{connection_id}/test")
        print(f"测试连接: {response.status_code}")
        print(f"响应: {response.json()}")
        
        return connection_id
    
    return None

def test_archive_tasks(connection_id):
    """测试归档任务API"""
    print("\n=== 测试归档任务API ===")
    
    # 获取任务列表
    response = requests.get(f"{BASE_URL}/archive-tasks")
    print(f"获取任务列表: {response.status_code}")
    print(f"响应: {response.json()}")
    
    if connection_id:
        # 创建新任务
        new_task = {
            "name": "测试归档任务",
            "description": "这是一个测试任务",
            "database_id": connection_id,
            "table_name": "TEST_TABLE",
            "date_field": "CREATE_DATE",
            "compression_enabled": True,
            "delete_after_archive": False,
            "is_active": True
        }
        
        response = requests.post(f"{BASE_URL}/archive-tasks", json=new_task)
        print(f"创建任务: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            task_id = response.json()['data']['id']
            
            # 执行任务（这会失败，因为没有真实的Oracle连接）
            response = requests.post(f"{BASE_URL}/archive-tasks/{task_id}/execute")
            print(f"执行任务: {response.status_code}")
            print(f"响应: {response.json()}")
            
            return task_id
    
    return None

def test_dashboard():
    """测试仪表板API"""
    print("\n=== 测试仪表板API ===")
    
    # 获取概览数据
    response = requests.get(f"{BASE_URL}/dashboard/overview")
    print(f"获取概览: {response.status_code}")
    print(f"响应: {response.json()}")
    
    # 获取执行统计
    response = requests.get(f"{BASE_URL}/task-executions/statistics")
    print(f"获取统计: {response.status_code}")
    print(f"响应: {response.json()}")

def test_system_logs():
    """测试系统日志API"""
    print("\n=== 测试系统日志API ===")
    
    # 获取日志列表
    response = requests.get(f"{BASE_URL}/system-logs")
    print(f"获取日志列表: {response.status_code}")
    print(f"响应: {response.json()}")

def main():
    """主函数"""
    print("开始API测试...")
    
    try:
        # 测试数据库连接
        connection_id = test_database_connections()
        
        # 测试归档任务
        task_id = test_archive_tasks(connection_id)
        
        # 测试仪表板
        test_dashboard()
        
        # 测试系统日志
        test_system_logs()
        
        print("\n=== API测试完成 ===")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保应用正在运行")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == '__main__':
    main()
