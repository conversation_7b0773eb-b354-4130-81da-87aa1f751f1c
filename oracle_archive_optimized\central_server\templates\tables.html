{% extends "base.html" %}

{% block title %}表配置 - Oracle 分布式归档系统{% endblock %}

{% block content %}
<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>归档表配置</h2>
        <button class="btn btn-primary" onclick="showAddTableModal()">添加归档表</button>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Agent</th>
                <th>Schema</th>
                <th>表名</th>
                <th>保留天数</th>
                <th>定时计划</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for table in tables %}
            <tr>
                <td>{{ table[0] }}</td>
                <td>{{ table[1] }}</td>
                <td>{{ table[2] }}</td>
                <td>{{ table[3] }}</td>
                <td>{{ table[4] }} 天</td>
                <td>
                    <span style="font-size: 0.875rem;">
                        {% if table[0] in schedules %}
                            {{ schedules[table[0]] }}
                        {% else %}
                            <span style="color: #6c757d;">未设置</span>
                        {% endif %}
                    </span>
                    <button class="btn btn-sm" onclick="editSchedule({{ table[0] }})">⏰</button>
                </td>
                <td>
                    {% if table[5] %}
                        <span class="status status-online">启用</span>
                    {% else %}
                        <span class="status status-offline">禁用</span>
                    {% endif %}
                </td>
                <td>
                    <button class="btn btn-sm" onclick="editTable({{ table[0] }})">编辑</button>
                    <button class="btn btn-sm" onclick="toggleTable({{ table[0] }}, {{ table[5] }})">
                        {{ '禁用' if table[5] else '启用' }}
                    </button>
                    <button class="btn btn-sm btn-success" onclick="createTaskForTable({{ table[0] }})">立即归档</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteTable({{ table[0] }})">删除</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    {% if not tables %}
    <p style="text-align: center; color: #666; padding: 2rem;">暂无归档表配置，请点击"添加归档表"创建配置。</p>
    {% endif %}
</div>

<!-- 添加表配置模态框 -->
<div id="addTableModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="background: white; margin: 5% auto; padding: 2rem; width: 600px; border-radius: 8px; max-height: 80vh; overflow-y: auto;">
        <h3>添加归档表配置</h3>
        <form id="addTableForm">
            <div style="margin-bottom: 1rem;">
                <label>选择 Agent:</label>
                <select name="agent_id" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="">请选择Agent</option>
                    <!-- 这里需要动态加载Agent列表 -->
                </select>
            </div>
            <div style="margin-bottom: 1rem;">
                <label>Schema 名称:</label>
                <input type="text" name="schema_name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>表名:</label>
                <input type="text" name="table_name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>日期字段:</label>
                <input type="text" name="date_field" required placeholder="例如: CREATED_DATE" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>数据保留天数:</label>
                <input type="number" name="retention_days" value="90" required min="1" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>批处理大小:</label>
                <input type="number" name="batch_size" value="5000" required min="100" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>
                    <input type="checkbox" name="enabled" checked> 启用归档
                </label>
            </div>
            <div style="background: #f0f8ff; padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
                <p style="margin: 0; color: #004085;">
                    <strong>重要提示：</strong><br>
                    • 数据将被导出并验证后才会删除<br>
                    • 批处理大小建议为 5000 以避免锁表<br>
                    • 请确保日期字段为 DATE 类型
                </p>
            </div>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" class="btn" onclick="hideAddTableModal()">取消</button>
                <button type="submit" class="btn btn-primary">添加</button>
            </div>
        </form>
    </div>
</div>

<!-- 定时计划模态框 -->
<div id="scheduleModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="background: white; margin: 5% auto; padding: 2rem; width: 500px; border-radius: 8px;">
        <h3>设置定时计划</h3>
        <form id="scheduleForm">
            <input type="hidden" name="table_id" id="schedule_table_id">
            
            <div style="margin-bottom: 1rem;">
                <label>执行频率:</label>
                <select name="frequency" onchange="updateScheduleOptions()" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="">请选择</option>
                    <option value="daily">每天</option>
                    <option value="weekly">每周</option>
                    <option value="monthly">每月</option>
                    <option value="custom">自定义Cron</option>
                </select>
            </div>
            
            <div id="dailyOptions" style="display: none; margin-bottom: 1rem;">
                <label>执行时间:</label>
                <input type="time" name="daily_time" value="02:00" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <div id="weeklyOptions" style="display: none; margin-bottom: 1rem;">
                <label>星期几:</label>
                <select name="weekly_day" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="0">星期日</option>
                    <option value="1">星期一</option>
                    <option value="2">星期二</option>
                    <option value="3">星期三</option>
                    <option value="4">星期四</option>
                    <option value="5">星期五</option>
                    <option value="6">星期六</option>
                </select>
                <label style="margin-top: 0.5rem;">执行时间:</label>
                <input type="time" name="weekly_time" value="02:00" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <div id="monthlyOptions" style="display: none; margin-bottom: 1rem;">
                <label>每月第几天:</label>
                <input type="number" name="monthly_day" min="1" max="31" value="1" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                <label style="margin-top: 0.5rem;">执行时间:</label>
                <input type="time" name="monthly_time" value="02:00" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <div id="customOptions" style="display: none; margin-bottom: 1rem;">
                <label>Cron 表达式:</label>
                <input type="text" name="cron_expression" placeholder="0 2 * * *" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                <small style="color: #6c757d;">格式: 分 时 日 月 周</small>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label>
                    <input type="checkbox" name="enabled" checked> 启用定时任务
                </label>
            </div>
            
            <div style="background: #d1ecf1; padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
                <p style="margin: 0; color: #0c5460;">
                    <strong>提示：</strong><br>
                    • 建议在业务低峰期执行归档任务<br>
                    • 不同表的执行时间最好错开<br>
                    • 时间为服务器时区
                </p>
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" class="btn" onclick="hideScheduleModal()">取消</button>
                <button type="button" class="btn btn-danger" onclick="removeSchedule()">删除计划</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>

<script>
// 页面加载时获取Agent列表
document.addEventListener('DOMContentLoaded', function() {
    loadAgentList();
});

function loadAgentList() {
    // 这里应该从API获取Agent列表
    // 临时硬编码
    const agentSelect = document.querySelector('select[name="agent_id"]');
    agentSelect.innerHTML = '<option value="">请选择Agent</option>';
    // 实际应该从 /api/agents 获取
}

function showAddTableModal() {
    document.getElementById('addTableModal').style.display = 'block';
}

function hideAddTableModal() {
    document.getElementById('addTableModal').style.display = 'none';
    document.getElementById('addTableForm').reset();
}

document.getElementById('addTableForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {};
    formData.forEach((value, key) => {
        if (key === 'enabled') {
            data[key] = formData.get(key) === 'on';
        } else {
            data[key] = value;
        }
    });
    
    // 这里应该调用API添加表配置
    alert('添加表配置功能待实现');
    console.log('表配置数据:', data);
});

function editTable(tableId) {
    alert('编辑表配置: ' + tableId);
    // 可以复用添加模态框，填充现有数据
}

function toggleTable(tableId, currentStatus) {
    const action = currentStatus ? '禁用' : '启用';
    if (confirm(`确定要${action}此归档配置吗？`)) {
        // 调用API切换状态
        alert(`${action}功能待实现`);
    }
}

function createTaskForTable(tableId) {
    if (confirm('确定要立即为此表创建归档任务吗？')) {
        // 调用API创建任务
        fetch('/api/task/create', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({table_id: tableId})
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('任务创建成功');
                // 可以跳转到任务页面
                window.location.href = '/tasks';
            } else {
                alert('创建失败: ' + result.error);
            }
        });
    }
}

function deleteTable(tableId) {
    if (confirm('确定要删除此归档配置吗？此操作不可恢复！')) {
        // 调用API删除配置
        alert('删除功能待实现');
    }
}

// 定时计划相关函数
function editSchedule(tableId) {
    document.getElementById('schedule_table_id').value = tableId;
    document.getElementById('scheduleModal').style.display = 'block';
    
    // 加载现有的计划
    fetch(`/api/schedule/get/${tableId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success && result.schedule) {
            const schedule = result.schedule;
            // 解析Cron表达式
            const parts = schedule.cron_expression.split(' ');
            if (parts.length >= 6) {
                // 尝试识别模式
                if (parts[3] === '*' && parts[4] === '*' && parts[5] === '*') {
                    // 每天
                    document.querySelector('select[name="frequency"]').value = 'daily';
                    document.querySelector('input[name="daily_time"]').value = `${parts[2].padStart(2,'0')}:${parts[1].padStart(2,'0')}`;
                    updateScheduleOptions();
                } else if (parts[3] === '*' && parts[4] === '*' && parts[5] !== '*') {
                    // 每周
                    document.querySelector('select[name="frequency"]').value = 'weekly';
                    document.querySelector('select[name="weekly_day"]').value = parts[5];
                    document.querySelector('input[name="weekly_time"]').value = `${parts[2].padStart(2,'0')}:${parts[1].padStart(2,'0')}`;
                    updateScheduleOptions();
                } else {
                    // 自定义
                    document.querySelector('select[name="frequency"]').value = 'custom';
                    document.querySelector('input[name="cron_expression"]').value = schedule.cron_expression;
                    updateScheduleOptions();
                }
            }
            document.querySelector('input[name="enabled"]').checked = schedule.enabled;
        }
    });
}

function hideScheduleModal() {
    document.getElementById('scheduleModal').style.display = 'none';
    document.getElementById('scheduleForm').reset();
}

function updateScheduleOptions() {
    const frequency = document.querySelector('select[name="frequency"]').value;
    
    // 隐藏所有选项
    document.getElementById('dailyOptions').style.display = 'none';
    document.getElementById('weeklyOptions').style.display = 'none';
    document.getElementById('monthlyOptions').style.display = 'none';
    document.getElementById('customOptions').style.display = 'none';
    
    // 显示对应的选项
    switch(frequency) {
        case 'daily':
            document.getElementById('dailyOptions').style.display = 'block';
            break;
        case 'weekly':
            document.getElementById('weeklyOptions').style.display = 'block';
            break;
        case 'monthly':
            document.getElementById('monthlyOptions').style.display = 'block';
            break;
        case 'custom':
            document.getElementById('customOptions').style.display = 'block';
            break;
    }
}

function removeSchedule() {
    if (confirm('确定要删除此定时计划吗？')) {
        const tableId = document.getElementById('schedule_table_id').value;
        
        fetch('/api/schedule/delete', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({table_id: tableId})
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('定时计划已删除');
                location.reload();
            } else {
                alert('删除失败: ' + result.error);
            }
        });
    }
}

document.getElementById('scheduleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const frequency = formData.get('frequency');
    let cronExpression = '';
    
    // 根据选择生成Cron表达式
    switch(frequency) {
        case 'daily':
            const dailyTime = formData.get('daily_time').split(':');
            cronExpression = `0 ${dailyTime[1]} ${dailyTime[0]} * * *`;
            break;
        case 'weekly':
            const weeklyTime = formData.get('weekly_time').split(':');
            const weeklyDay = formData.get('weekly_day');
            cronExpression = `0 ${weeklyTime[1]} ${weeklyTime[0]} * * ${weeklyDay}`;
            break;
        case 'monthly':
            const monthlyTime = formData.get('monthly_time').split(':');
            const monthlyDay = formData.get('monthly_day');
            cronExpression = `0 ${monthlyTime[1]} ${monthlyTime[0]} ${monthlyDay} * *`;
            break;
        case 'custom':
            cronExpression = formData.get('cron_expression');
            break;
    }
    
    const scheduleData = {
        table_id: formData.get('table_id'),
        cron_expression: cronExpression,
        enabled: formData.get('enabled') === 'on'
    };
    
    // 调用API保存计划
    fetch('/api/schedule/create', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(scheduleData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(`定时计划已保存\n下次执行时间: ${new Date(result.next_run).toLocaleString()}`);
            location.reload();
        } else {
            alert('保存失败: ' + result.error);
        }
    });
});
</script>
{% endblock %} 