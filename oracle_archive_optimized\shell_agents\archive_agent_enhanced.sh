#!/bin/bash
#############################################################################
# Oracle Archive Agent - 增强版（带数据验证）
# 基于原有单表归档脚本的核心功能，增加数据完整性验证
#############################################################################

# 默认配置
AGENT_HOME="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${AGENT_HOME}/agent.conf"
LOG_DIR="${AGENT_HOME}/logs"
WORK_DIR="${AGENT_HOME}/work"
TEMP_DIR="${AGENT_HOME}/temp"

# 日志级别
LOG_LEVEL="INFO"

# 创建必要的目录
mkdir -p "$LOG_DIR" "$WORK_DIR" "$TEMP_DIR"

# 日志文件
LOG_FILE="${LOG_DIR}/archive_agent_$(date +%Y%m%d).log"

#############################################################################
# 日志函数
#############################################################################
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
}

log_error() {
    log "ERROR" "$@"
}

log_debug() {
    if [ "$LOG_LEVEL" = "DEBUG" ]; then
        log "DEBUG" "$@"
    fi
}

log_warn() {
    log "WARN" "$@"
}

#############################################################################
# 配置加载函数
#############################################################################
load_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    # 加载配置文件
    source "$CONFIG_FILE"
    
    # 验证必要的配置项
    local required_vars=(
        "ORACLE_HOME"
        "ORACLE_SID"
        "DB_USER"
        "DB_PASS"
        "CENTRAL_SERVER"
        "AGENT_ID"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "缺少配置项: $var"
            return 1
        fi
    done
    
    # 设置默认值
    EXPORT_METHOD=${EXPORT_METHOD:-"exp"}  # exp 或 expdp
    VERIFY_BEFORE_DELETE=${VERIFY_BEFORE_DELETE:-"true"}  # 删除前验证
    BATCH_DELETE_SIZE=${BATCH_DELETE_SIZE:-5000}  # 批量删除大小
    SEND_NOTIFICATION=${SEND_NOTIFICATION:-"true"}  # 发送通知
    
    return 0
}

#############################################################################
# 统计要归档的记录数
#############################################################################
count_records() {
    local table_name=$1
    local schema_name=$2
    local date_field=$3
    local retention_days=$4
    
    log_info "统计要归档的记录数: ${schema_name}.${table_name}"
    
    # 设置Oracle环境变量
    export ORACLE_HOME
    export ORACLE_SID
    export PATH=$ORACLE_HOME/bin:$PATH
    export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH
    
    # 计算截止日期
    local cutoff_date=$(date -d "${retention_days} days ago" +%Y-%m-%d)
    
    # 创建统计SQL
    local sql_file="${TEMP_DIR}/count_${table_name}.sql"
    cat > "$sql_file" <<EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET VERIFY OFF
SET HEADING OFF
SET TERMOUT OFF

SELECT COUNT(*) FROM ${schema_name}.${table_name}
WHERE ${date_field} < TO_DATE('${cutoff_date}', 'YYYY-MM-DD');

EXIT;
EOF
    
    # 执行统计
    local count_result=$(sqlplus -S "${DB_USER}/${DB_PASS}" @"$sql_file" | tr -d '[:space:]')
    
    # 清理临时文件
    rm -f "$sql_file"
    
    if [[ "$count_result" =~ ^[0-9]+$ ]]; then
        log_info "统计完成，需要归档的记录数: $count_result"
        echo "$count_result"
        return 0
    else
        log_error "统计失败，返回值: $count_result"
        echo "0"
        return 1
    fi
}

#############################################################################
# Oracle数据导出函数（支持exp和expdp）
#############################################################################
export_oracle_data() {
    local table_name=$1
    local schema_name=$2
    local date_field=$3
    local retention_days=$4
    local output_file=$5
    
    log_info "开始导出表: ${schema_name}.${table_name}"
    
    # 设置Oracle环境变量
    export ORACLE_HOME
    export ORACLE_SID
    export PATH=$ORACLE_HOME/bin:$PATH
    export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH
    export NLS_LANG=${NLS_LANG:-"AMERICAN_AMERICA.AL32UTF8"}
    
    # 计算截止日期
    local cutoff_date=$(date -d "${retention_days} days ago" +%Y-%m-%d)
    local timestamp=$(date +%Y%m%d-%H%M%S)
    
    if [ "$EXPORT_METHOD" = "expdp" ]; then
        # 使用数据泵导出
        log_info "使用数据泵(expdp)导出数据"
        
        # 创建参数文件
        local par_file="${TEMP_DIR}/export_${table_name}.par"
        cat > "$par_file" <<EOF
userid=${DB_USER}/${DB_PASS}
tables=${schema_name}.${table_name}
directory=DATA_PUMP_DIR
dumpfile=${table_name}_${timestamp}.dmp
logfile=${table_name}_${timestamp}.log
query="${schema_name}.${table_name}:WHERE ${date_field} < TO_DATE('${cutoff_date}', 'YYYY-MM-DD')"
compression=ALL
EOF
        
        # 执行导出
        expdp parfile="$par_file"
        local exit_code=$?
        
        # 移动文件到输出位置
        if [ $exit_code -eq 0 ]; then
            # 需要从DATA_PUMP_DIR移动文件
            local dp_dir=$(sqlplus -S "${DB_USER}/${DB_PASS}" <<EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT directory_path FROM dba_directories WHERE directory_name='DATA_PUMP_DIR';
EXIT;
EOF
)
            dp_dir=$(echo "$dp_dir" | tr -d '[:space:]')
            mv "${dp_dir}/${table_name}_${timestamp}.dmp" "$output_file" 2>/dev/null
        fi
        
        rm -f "$par_file"
        
    else
        # 使用传统exp导出
        log_info "使用传统导出(exp)导出数据"
        
        local log_file="${output_file}.log"
        
        # 构建exp命令
        exp "${DB_USER}/${DB_PASS}" \
            TABLES="${schema_name}.${table_name}" \
            QUERY="WHERE ${date_field} < TO_DATE('${cutoff_date}', 'YYYY-MM-DD')" \
            FILE="$output_file" \
            LOG="$log_file" \
            INDEXES=n \
            GRANTS=n \
            STATISTICS=NONE \
            COMPRESS=n \
            CONSISTENT=y
        
        local exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        log_info "导出完成"
        return 0
    else
        log_error "导出失败，退出码: $exit_code"
        return 1
    fi
}

#############################################################################
# 验证导出的数据行数
#############################################################################
verify_export() {
    local export_file=$1
    local expected_count=$2
    local table_name=$3
    
    log_info "验证导出数据完整性"
    
    # 获取导出日志文件
    local log_file="${export_file}.log"
    
    if [ ! -f "$log_file" ]; then
        log_error "导出日志文件不存在: $log_file"
        return 1
    fi
    
    # 从日志中提取导出的行数
    local exported_count=$(grep -E "exported|rows exported" "$log_file" | grep -i "$table_name" | awk '{
        for(i=1; i<=NF; i++) {
            if ($i ~ /^[0-9]+$/ && $(i+1) == "rows") {
                print $i
                exit
            }
        }
    }')
    
    if [ -z "$exported_count" ]; then
        # 尝试其他格式
        exported_count=$(grep -oE "[0-9]+ rows exported" "$log_file" | head -1 | awk '{print $1}')
    fi
    
    log_info "期望导出行数: $expected_count"
    log_info "实际导出行数: $exported_count"
    
    if [ "$exported_count" = "$expected_count" ]; then
        log_info "数据验证通过，行数匹配"
        return 0
    else
        log_error "数据验证失败！行数不匹配"
        log_error "期望: $expected_count, 实际: $exported_count"
        return 1
    fi
}

#############################################################################
# 增强的删除已归档数据函数
#############################################################################
delete_archived_data() {
    local table_name=$1
    local schema_name=$2
    local date_field=$3
    local retention_days=$4
    local batch_size=${5:-5000}
    local verified=${6:-false}
    
    if [ "${DELETE_AFTER_ARCHIVE}" != "true" ]; then
        log_info "跳过数据删除（配置为不删除）"
        return 0
    fi
    
    if [ "${VERIFY_BEFORE_DELETE}" = "true" ] && [ "$verified" != "true" ]; then
        log_error "数据未通过验证，拒绝删除操作"
        return 1
    fi
    
    log_info "开始删除已归档数据: ${schema_name}.${table_name}"
    log_info "批量删除大小: $batch_size"
    
    # 计算截止日期
    local cutoff_date=$(date -d "${retention_days} days ago" +%Y-%m-%d)
    
    # 创建删除SQL（与原脚本保持一致的逻辑）
    local sql_file="${TEMP_DIR}/delete_${table_name}.sql"
    cat > "$sql_file" <<EOF
SET SERVEROUTPUT ON
SET TIMING ON

DECLARE
    v_deleted NUMBER := 0;
    v_total_deleted NUMBER := 0;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
BEGIN
    v_start_time := SYSTIMESTAMP;
    
    LOOP
        DELETE FROM ${schema_name}.${table_name}
        WHERE ${date_field} < TO_DATE('${cutoff_date}', 'YYYY-MM-DD')
            AND ROWNUM <= ${batch_size};
        
        v_deleted := SQL%ROWCOUNT;
        v_total_deleted := v_total_deleted + v_deleted;
        
        IF SQL%NOTFOUND OR v_deleted = 0 THEN
            EXIT;
        END IF;
        
        COMMIT;
        
        -- 输出进度
        DBMS_OUTPUT.PUT_LINE('Deleted ' || v_deleted || ' rows, total: ' || v_total_deleted);
        
        -- 避免长时间锁表，稍作暂停
        DBMS_LOCK.SLEEP(0.1);
    END LOOP;
    
    v_end_time := SYSTIMESTAMP;
    
    DBMS_OUTPUT.PUT_LINE('=================================');
    DBMS_OUTPUT.PUT_LINE('Total deleted: ' || v_total_deleted || ' rows');
    DBMS_OUTPUT.PUT_LINE('Start time: ' || TO_CHAR(v_start_time, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('End time: ' || TO_CHAR(v_end_time, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('Duration: ' || EXTRACT(MINUTE FROM (v_end_time - v_start_time)) || ' minutes');
    
    -- 收集表统计信息
    DBMS_STATS.GATHER_TABLE_STATS(
        OWNNAME => '${schema_name}',
        TABNAME => '${table_name}',
        CASCADE => TRUE
    );
    
    DBMS_OUTPUT.PUT_LINE('Table statistics updated');
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        NULL;
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error: ' || SQLERRM);
        RAISE;
END;
/
EXIT;
EOF
    
    # 执行删除
    log_debug "执行数据删除..."
    sqlplus -S "${DB_USER}/${DB_PASS}" @"$sql_file" | tee -a "$LOG_FILE"
    local exit_code=$?
    
    # 清理临时文件
    rm -f "$sql_file"
    
    if [ $exit_code -eq 0 ]; then
        log_info "数据删除完成"
        return 0
    else
        log_error "数据删除失败，退出码: $exit_code"
        return 1
    fi
}

#############################################################################
# 文件压缩函数（保持原有功能）
#############################################################################
compress_file() {
    local input_file=$1
    local compression_type=${COMPRESSION_TYPE:-"tar.gz"}
    
    if [ ! -f "$input_file" ]; then
        log_error "输入文件不存在: $input_file"
        return 1
    fi
    
    local output_file=""
    local timestamp=$(date +%Y%m%d-%H%M%S)
    
    case "$compression_type" in
        "tar.gz")
            output_file="${input_file%.dmp}_${timestamp}.tar.gz"
            log_info "使用tar.gz压缩文件..."
            tar czf "$output_file" -C "$(dirname "$input_file")" "$(basename "$input_file")" "$(basename "$input_file").log" 2>/dev/null
            ;;
        "gzip")
            output_file="${input_file}.gz"
            log_info "使用gzip压缩文件..."
            gzip -c "$input_file" > "$output_file"
            ;;
        "zip")
            output_file="${input_file%.dmp}_${timestamp}.zip"
            log_info "使用zip压缩文件..."
            zip -j "$output_file" "$input_file" "${input_file}.log" 2>/dev/null
            ;;
        *)
            log_error "不支持的压缩类型: $compression_type"
            return 1
            ;;
    esac
    
    if [ -f "$output_file" ]; then
        local original_size=$(stat -c%s "$input_file" 2>/dev/null || echo "0")
        local compressed_size=$(stat -c%s "$output_file" 2>/dev/null || echo "0")
        local ratio=0
        if [ $original_size -gt 0 ]; then
            ratio=$((100 - (compressed_size * 100 / original_size)))
        fi
        log_info "压缩完成，压缩率: ${ratio}%"
        echo "$output_file"
        return 0
    else
        log_error "压缩失败"
        return 1
    fi
}

#############################################################################
# 发送邮件通知
#############################################################################
send_notification() {
    local task_id=$1
    local status=$2
    local details=$3
    local log_content=$4
    
    if [ "${SEND_NOTIFICATION}" != "true" ]; then
        return 0
    fi
    
    local subject="${ORACLE_SID} Archive Task #${task_id} - ${status}"
    local email_body="Archive Task Report
===================
Task ID: ${task_id}
Status: ${status}
Agent: ${AGENT_ID}
Database: ${ORACLE_SID}
Time: $(date '+%Y-%m-%d %H:%M:%S')

Details:
${details}

Log Summary:
${log_content}
"
    
    # 使用mailx发送邮件（与原脚本保持一致）
    if command -v mailx >/dev/null 2>&1; then
        echo "$email_body" | mailx -s "$subject" ${NOTIFY_EMAIL} -- -f ${SMTP_FROM:-"archive@localhost"}
        log_info "邮件通知已发送到: ${NOTIFY_EMAIL}"
    else
        log_warn "mailx命令不可用，跳过邮件通知"
    fi
}

#############################################################################
# 执行归档任务（增强版）
#############################################################################
execute_archive_task() {
    local task_json=$1
    
    # 解析任务参数
    local task_id=$(echo "$task_json" | jq -r '.task_id')
    local table_name=$(echo "$task_json" | jq -r '.table_name')
    local schema_name=$(echo "$task_json" | jq -r '.schema_name')
    local date_field=$(echo "$task_json" | jq -r '.date_field')
    local retention_days=$(echo "$task_json" | jq -r '.retention_days')
    local batch_size=$(echo "$task_json" | jq -r '.batch_size // 5000')
    
    log_info "==============================================="
    log_info "开始执行归档任务 #${task_id}"
    log_info "表: ${schema_name}.${table_name}"
    log_info "保留天数: ${retention_days}"
    log_info "==============================================="
    
    # 初始化任务状态
    local task_status="failed"
    local error_message=""
    local expected_count=0
    local actual_count=0
    local verified=false
    
    # 报告任务开始
    report_status "$task_id" "running" "任务开始执行" "{\"start_time\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"}"
    
    # 生成文件名
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local export_file="${WORK_DIR}/${schema_name}_${table_name}_${timestamp}.dmp"
    
    # 1. 统计要归档的记录数
    expected_count=$(count_records "$table_name" "$schema_name" "$date_field" "$retention_days")
    
    if [ "$expected_count" = "0" ]; then
        log_info "没有需要归档的数据"
        report_status "$task_id" "completed" "没有需要归档的数据" "{\"records\": 0}"
        send_notification "$task_id" "COMPLETED" "No data to archive" "Table: ${schema_name}.${table_name}\nRecords: 0"
        return 0
    fi
    
    # 2. 导出数据
    if export_oracle_data "$table_name" "$schema_name" "$date_field" "$retention_days" "$export_file"; then
        
        # 3. 验证导出的数据
        if [ "${VERIFY_BEFORE_DELETE}" = "true" ]; then
            if verify_export "$export_file" "$expected_count" "$table_name"; then
                verified=true
                actual_count=$expected_count
                log_info "数据验证通过"
            else
                error_message="数据验证失败：导出行数与预期不符"
                log_error "$error_message"
                report_status "$task_id" "failed" "$error_message" "{\"error\": \"Data verification failed\"}"
                send_notification "$task_id" "FAILED" "$error_message" "$(tail -n 50 "$LOG_FILE")"
                return 1
            fi
        else
            verified=true
            actual_count=$expected_count
        fi
        
        # 4. 压缩文件
        local compressed_file=$(compress_file "$export_file")
        if [ -z "$compressed_file" ]; then
            error_message="文件压缩失败"
            report_status "$task_id" "failed" "$error_message" "{\"error\": \"Compression failed\"}"
            rm -f "$export_file" "${export_file}.log"
            return 1
        fi
        
        # 5. 上传文件
        if upload_file "$compressed_file" "$task_id"; then
            
            # 6. 删除已归档数据（仅在验证通过后）
            if [ "$verified" = "true" ]; then
                if delete_archived_data "$table_name" "$schema_name" "$date_field" "$retention_days" "$batch_size" "true"; then
                    task_status="completed"
                    log_info "归档任务完成，数据已删除"
                else
                    task_status="completed_with_warning"
                    log_warn "归档完成但删除数据失败"
                fi
            else
                task_status="completed_no_delete"
                log_warn "归档完成但未删除数据（验证未通过）"
            fi
            
        else
            error_message="文件上传失败"
            report_status "$task_id" "failed" "$error_message" "{\"error\": \"Upload failed\"}"
            send_notification "$task_id" "FAILED" "$error_message" "$(tail -n 50 "$LOG_FILE")"
            rm -f "$export_file" "${export_file}.log" "$compressed_file"
            return 1
        fi
        
    else
        error_message="数据导出失败"
        report_status "$task_id" "failed" "$error_message" "{\"error\": \"Export failed\"}"
        send_notification "$task_id" "FAILED" "$error_message" "$(tail -n 50 "$LOG_FILE")"
        return 1
    fi
    
    # 7. 清理本地文件
    rm -f "$export_file" "${export_file}.log" "$compressed_file"
    
    # 8. 报告任务完成
    local file_size=$(stat -c%s "$compressed_file" 2>/dev/null || echo "0")
    
    local details="{
        \"end_time\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
        \"records_expected\": ${expected_count},
        \"records_exported\": ${actual_count},
        \"verified\": ${verified},
        \"file_size\": ${file_size},
        \"file_name\": \"$(basename "$compressed_file")\"
    }"
    
    report_status "$task_id" "$task_status" "任务完成" "$details"
    
    # 9. 发送邮件通知
    local notification_details="Table: ${schema_name}.${table_name}
Expected Records: ${expected_count}
Exported Records: ${actual_count}
Data Verified: ${verified}
File Size: $((file_size / 1024 / 1024)) MB
Status: ${task_status}"
    
    send_notification "$task_id" "${task_status^^}" "$notification_details" "$(tail -n 100 "$LOG_FILE")"
    
    log_info "归档任务 #${task_id} 执行完成"
    log_info "==============================================="
    
    return 0
}

#############################################################################
# 其他函数保持不变...
#############################################################################

# 向中央服务器报告状态
report_status() {
    local task_id=$1
    local status=$2
    local message=$3
    local details=$4
    
    log_debug "向中央服务器报告状态: task_id=$task_id, status=$status"
    
    local json_data="{
        \"task_id\": ${task_id},
        \"agent_id\": \"${AGENT_ID}\",
        \"status\": \"${status}\",
        \"message\": \"${message}\",
        \"details\": ${details:-{}},
        \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
    }"
    
    curl -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        "${CENTRAL_SERVER}/api/task/status" \
        -s -o /dev/null
    
    if [ $? -eq 0 ]; then
        log_debug "状态报告成功"
        return 0
    else
        log_error "状态报告失败"
        return 1
    fi
}

# 文件上传函数
upload_file() {
    local file_path=$1
    local task_id=$2
    local upload_type=${UPLOAD_TYPE:-"scp"}
    
    if [ ! -f "$file_path" ]; then
        log_error "上传文件不存在: $file_path"
        return 1
    fi
    
    local remote_path=""
    
    case "$upload_type" in
        "scp")
            remote_path="${UPLOAD_SERVER}:${UPLOAD_PATH}/$(basename "$file_path")"
            log_info "使用SCP上传文件到: $remote_path"
            scp -o StrictHostKeyChecking=no "$file_path" "$remote_path"
            ;;
        "sftp")
            log_info "使用SFTP上传文件..."
            sftp -o StrictHostKeyChecking=no "${UPLOAD_SERVER}" <<EOF
cd ${UPLOAD_PATH}
put ${file_path}
bye
EOF
            ;;
        "http")
            log_info "使用HTTP上传文件..."
            curl -X POST \
                -F "file=@${file_path}" \
                -F "task_id=${task_id}" \
                -F "agent_id=${AGENT_ID}" \
                "${CENTRAL_SERVER}/api/upload" \
                -s -o /dev/null
            ;;
        "local")
            # 本地移动（用于测试）
            log_info "移动文件到本地备份目录..."
            mv "$file_path" "${UPLOAD_PATH}/"
            ;;
        *)
            log_error "不支持的上传类型: $upload_type"
            return 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        log_info "文件上传成功"
        return 0
    else
        log_error "文件上传失败"
        return 1
    fi
}

# 心跳函数
send_heartbeat() {
    while true; do
        curl -X POST \
            -H "Content-Type: application/json" \
            -d "{\"agent_id\": \"${AGENT_ID}\", \"status\": \"online\"}" \
            "${CENTRAL_SERVER}/api/agent/heartbeat" \
            -s -o /dev/null
        
        sleep ${HEARTBEAT_INTERVAL:-60}
    done
}

#############################################################################
# 主函数
#############################################################################
main() {
    local action=${1:-"help"}
    
    case "$action" in
        "start")
            log_info "Oracle Archive Agent 启动"
            
            # 加载配置
            if ! load_config; then
                log_error "加载配置失败"
                exit 1
            fi
            
            # 启动心跳（后台进程）
            send_heartbeat &
            HEARTBEAT_PID=$!
            
            log_info "Agent 已启动，等待任务..."
            log_info "心跳进程PID: $HEARTBEAT_PID"
            
            # 保存PID
            echo $$ > "${AGENT_HOME}/agent.pid"
            echo $HEARTBEAT_PID > "${AGENT_HOME}/heartbeat.pid"
            
            # 等待信号
            trap "log_info 'Agent 停止'; kill $HEARTBEAT_PID 2>/dev/null; exit 0" SIGTERM SIGINT
            
            while true; do
                sleep 10
            done
            ;;
            
        "execute")
            # 执行单个任务（由中央服务器通过SSH调用）
            local task_json=$2
            if [ -z "$task_json" ]; then
                log_error "缺少任务参数"
                exit 1
            fi
            
            # 加载配置
            if ! load_config; then
                log_error "加载配置失败"
                exit 1
            fi
            
            # 执行任务
            execute_archive_task "$task_json"
            ;;
            
        "test")
            # 测试模式
            log_info "测试模式"
            
            # 加载配置
            if ! load_config; then
                log_error "加载配置失败"
                exit 1
            fi
            
            # 测试数据库连接
            log_info "测试Oracle连接..."
            echo "SELECT 'OK' FROM DUAL;" | sqlplus -S "${DB_USER}/${DB_PASS}" | grep -q "OK"
            if [ $? -eq 0 ]; then
                log_info "数据库连接成功"
            else
                log_error "数据库连接失败"
                exit 1
            fi
            
            # 测试中央服务器连接
            log_info "测试中央服务器连接..."
            curl -s "${CENTRAL_SERVER}/api/health" > /dev/null
            if [ $? -eq 0 ]; then
                log_info "中央服务器连接成功"
            else
                log_error "中央服务器连接失败"
                exit 1
            fi
            
            # 测试邮件发送
            if [ "${SEND_NOTIFICATION}" = "true" ] && command -v mailx >/dev/null 2>&1; then
                log_info "测试邮件发送..."
                echo "Test email from ${AGENT_ID}" | mailx -s "Archive Agent Test" ${NOTIFY_EMAIL}
                log_info "测试邮件已发送"
            fi
            
            log_info "所有测试通过"
            ;;
            
        "count")
            # 统计模式（用于测试）
            if [ $# -lt 5 ]; then
                echo "用法: $0 count <table_name> <schema_name> <date_field> <retention_days>"
                exit 1
            fi
            
            # 加载配置
            if ! load_config; then
                log_error "加载配置失败"
                exit 1
            fi
            
            count_records "$2" "$3" "$4" "$5"
            ;;
            
        "stop")
            # 停止Agent
            if [ -f "${AGENT_HOME}/agent.pid" ]; then
                local pid=$(cat "${AGENT_HOME}/agent.pid")
                kill $pid 2>/dev/null
                rm -f "${AGENT_HOME}/agent.pid"
            fi
            
            if [ -f "${AGENT_HOME}/heartbeat.pid" ]; then
                local hb_pid=$(cat "${AGENT_HOME}/heartbeat.pid")
                kill $hb_pid 2>/dev/null
                rm -f "${AGENT_HOME}/heartbeat.pid"
            fi
            
            log_info "Agent 已停止"
            ;;
            
        *)
            echo "Oracle Archive Agent - 增强版"
            echo "用法: $0 {start|execute|test|count|stop}"
            echo ""
            echo "命令:"
            echo "  start    - 启动Agent服务"
            echo "  execute  - 执行归档任务（JSON参数）"
            echo "  test     - 测试连接和配置"
            echo "  count    - 统计要归档的记录数"
            echo "  stop     - 停止Agent服务"
            echo ""
            echo "特性:"
            echo "  - 归档前统计记录数"
            echo "  - 导出后验证数据完整性"
            echo "  - 批量删除避免锁表"
            echo "  - 自动收集表统计信息"
            echo "  - 邮件通知功能"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 