from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
import yaml
import os
import logging
from logging.handlers import RotatingFileHandler

db = SQLAlchemy()
migrate = Migrate()

def create_app(config_path='config/archive_config.yaml'):
    # 确保使用正确的模板目录
    import os
    template_dir = os.path.abspath('templates')
    static_dir = os.path.abspath('static')
    app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 配置Flask应用
    app.config['SECRET_KEY'] = config['app']['secret_key']

    # 处理数据库URL，确保使用绝对路径
    db_url = config['database']['url']
    if db_url.startswith('sqlite:///'):
        # 将相对路径转换为绝对路径
        db_path = db_url.replace('sqlite:///', '')
        if not os.path.isabs(db_path):
            db_path = os.path.abspath(db_path)
        db_url = f'sqlite:///{db_path}'

    app.config['SQLALCHEMY_DATABASE_URI'] = db_url
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = config['database']['echo']
    
    # 存储配置到app.config中供其他模块使用
    app.config['ARCHIVE_CONFIG'] = config
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    CORS(app)
    
    # 配置日志
    setup_logging(app, config['logging'])
    
    # 注册蓝图
    from app.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    # 注册路由
    @app.route('/')
    def index():
        from flask import render_template
        return render_template('index.html')

    @app.route('/database-connections')
    def database_connections():
        from flask import render_template
        return render_template('database_connections.html')

    @app.route('/archive-tasks')
    def archive_tasks():
        from flask import render_template
        return render_template('archive_tasks.html')

    @app.route('/execution-history')
    def execution_history():
        from flask import render_template
        return render_template('execution_history.html')

    @app.route('/system-settings')
    def system_settings():
        from flask import render_template
        return render_template('system_settings.html')

    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    # 启动调度器
    if not config.get('app', {}).get('debug', False):
        try:
            from app.services.scheduler_service import scheduler_service
            scheduler_service.start()
        except Exception as e:
            app.logger.error(f'启动调度器失败: {str(e)}')
    
    return app

def setup_logging(app, logging_config):
    """配置日志系统"""
    if not app.debug:
        # 确保日志目录存在
        log_dir = os.path.dirname(logging_config['file'])
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 配置文件日志处理器
        file_handler = RotatingFileHandler(
            logging_config['file'],
            maxBytes=10*1024*1024,  # 10MB
            backupCount=logging_config['backup_count']
        )
        file_handler.setFormatter(logging.Formatter(
            logging_config['format']
        ))
        file_handler.setLevel(getattr(logging, logging_config['level']))
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(getattr(logging, logging_config['level']))
        app.logger.info('数据库归档管理平台启动')
