{% extends "base.html" %}

{% block title %}任务管理 - Oracle 分布式归档系统{% endblock %}

{% block content %}
<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>归档任务管理</h2>
        <div style="display: flex; gap: 1rem;">
            <select id="statusFilter" onchange="filterTasks()" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">所有状态</option>
                <option value="pending">待处理</option>
                <option value="running">运行中</option>
                <option value="completed">已完成</option>
                <option value="failed">失败</option>
            </select>
            <button class="btn btn-primary" onclick="createBatchTasks()">批量创建任务</button>
            <button class="btn" onclick="refreshTasks()">刷新</button>
        </div>
    </div>
    
    <table id="tasksTable">
        <thead>
            <tr>
                <th>任务ID</th>
                <th>Agent</th>
                <th>表名</th>
                <th>状态</th>
                <th>开始时间</th>
                <th>结束时间</th>
                <th>导出行数</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for task in tasks %}
            <tr data-status="{{ task[3] }}">
                <td>#{{ task[0] }}</td>
                <td>{{ task[1] }}</td>
                <td>{{ task[2] }}</td>
                <td>
                    <span class="status status-{{ task[3] }}">
                        {% if task[3] == 'pending' %}
                            待处理
                        {% elif task[3] == 'dispatching' %}
                            分发中
                        {% elif task[3] == 'running' %}
                            运行中
                        {% elif task[3] == 'completed' %}
                            已完成
                        {% elif task[3] == 'completed_with_warning' %}
                            完成(有警告)
                        {% elif task[3] == 'completed_no_delete' %}
                            完成(未删除)
                        {% elif task[3] == 'failed' %}
                            失败
                        {% else %}
                            {{ task[3] }}
                        {% endif %}
                    </span>
                </td>
                <td>{{ task[4] or '-' }}</td>
                <td>{{ task[5] or '-' }}</td>
                <td>
                    {% if task[3] in ['completed', 'completed_with_warning', 'completed_no_delete'] %}
                        <span style="color: #28a745;">查看详情</span>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td>
                    <button class="btn btn-sm" onclick="viewTaskDetails({{ task[0] }})">详情</button>
                    {% if task[3] == 'failed' %}
                        <button class="btn btn-sm btn-primary" onclick="retryTask({{ task[0] }})">重试</button>
                    {% endif %}
                    {% if task[3] == 'running' %}
                        <button class="btn btn-sm btn-danger" onclick="stopTask({{ task[0] }})">停止</button>
                    {% endif %}
                    {% if task[6] %}
                        <button class="btn btn-sm btn-danger" onclick="showError('{{ task[6] }}')">查看错误</button>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    {% if not tasks %}
    <p style="text-align: center; color: #666; padding: 2rem;">暂无归档任务。</p>
    {% endif %}
</div>

<!-- 任务详情模态框 -->
<div id="taskDetailsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="background: white; margin: 5% auto; padding: 2rem; width: 700px; border-radius: 8px; max-height: 80vh; overflow-y: auto;">
        <h3>任务详情</h3>
        <div id="taskDetailsContent">
            <!-- 动态加载内容 -->
        </div>
        <div style="margin-top: 1rem; text-align: right;">
            <button class="btn" onclick="hideTaskDetailsModal()">关闭</button>
        </div>
    </div>
</div>

<!-- 任务统计 -->
<div class="card" style="margin-top: 2rem;">
    <h3>任务统计</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
        <div style="padding: 1rem; background: #f8f9fa; border-radius: 4px;">
            <h4 style="margin: 0; color: #666;">今日任务</h4>
            <p style="font-size: 2rem; margin: 0.5rem 0;">{{ tasks|length }}</p>
        </div>
        <div style="padding: 1rem; background: #d4edda; border-radius: 4px;">
            <h4 style="margin: 0; color: #155724;">成功率</h4>
            <p style="font-size: 2rem; margin: 0.5rem 0;">
                {% set completed = tasks|selectattr('3', 'equalto', 'completed')|list|length %}
                {% set total = tasks|length %}
                {{ ((completed / total * 100) if total > 0 else 0)|round(1) }}%
            </p>
        </div>
        <div style="padding: 1rem; background: #cce5ff; border-radius: 4px;">
            <h4 style="margin: 0; color: #004085;">平均耗时</h4>
            <p style="font-size: 2rem; margin: 0.5rem 0;">- 分钟</p>
        </div>
        <div style="padding: 1rem; background: #fff3cd; border-radius: 4px;">
            <h4 style="margin: 0; color: #856404;">待处理</h4>
            <p style="font-size: 2rem; margin: 0.5rem 0;">
                {{ tasks|selectattr('3', 'equalto', 'pending')|list|length }}
            </p>
        </div>
    </div>
</div>

<script>
function filterTasks() {
    const status = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('#tasksTable tbody tr');
    
    rows.forEach(row => {
        if (!status || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function refreshTasks() {
    location.reload();
}

function createBatchTasks() {
    if (confirm('确定要为所有启用的归档表创建任务吗？')) {
        fetch('/api/task/create', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert(`成功创建 ${result.task_count} 个任务`);
                location.reload();
            } else {
                alert('创建失败: ' + result.error);
            }
        });
    }
}

function viewTaskDetails(taskId) {
    // 这里应该从API获取任务详情
    const modal = document.getElementById('taskDetailsModal');
    const content = document.getElementById('taskDetailsContent');
    
    content.innerHTML = `
        <div style="display: grid; gap: 1rem;">
            <div>
                <strong>任务ID:</strong> #${taskId}
            </div>
            <div>
                <strong>状态:</strong> <span class="status status-completed">已完成</span>
            </div>
            <div>
                <strong>执行时间:</strong> 2025-06-18 14:30:00 - 14:35:23
            </div>
            <div>
                <strong>数据验证:</strong> 
                <span style="color: #28a745;">✓ 通过</span>
                <ul style="margin: 0.5rem 0;">
                    <li>预期行数: 5,806</li>
                    <li>导出行数: 5,806</li>
                    <li>验证结果: 匹配</li>
                </ul>
            </div>
            <div>
                <strong>文件信息:</strong>
                <ul style="margin: 0.5rem 0;">
                    <li>文件名: SCHEMA_TABLE_20250618_143000.tar.gz</li>
                    <li>文件大小: 23.5 MB</li>
                    <li>压缩率: 78%</li>
                </ul>
            </div>
            <div>
                <strong>执行日志:</strong>
                <pre style="background: #f8f9fa; padding: 1rem; border-radius: 4px; max-height: 200px; overflow-y: auto;">
[2025-06-18 14:30:00] [INFO] 开始执行归档任务 #${taskId}
[2025-06-18 14:30:01] [INFO] 统计要归档的记录数: SCHEMA.TABLE
[2025-06-18 14:30:02] [INFO] 统计完成，需要归档的记录数: 5806
[2025-06-18 14:30:03] [INFO] 开始导出表: SCHEMA.TABLE
[2025-06-18 14:32:15] [INFO] 导出完成
[2025-06-18 14:32:16] [INFO] 验证导出数据完整性
[2025-06-18 14:32:17] [INFO] 数据验证通过，行数匹配
[2025-06-18 14:32:18] [INFO] 使用tar.gz压缩文件...
[2025-06-18 14:32:45] [INFO] 压缩完成，压缩率: 78%
[2025-06-18 14:32:46] [INFO] 使用SCP上传文件到: backup-server:/backup/oracle_archives/
[2025-06-18 14:33:20] [INFO] 文件上传成功
[2025-06-18 14:33:21] [INFO] 开始删除已归档数据: SCHEMA.TABLE
[2025-06-18 14:35:22] [INFO] 数据删除完成
[2025-06-18 14:35:23] [INFO] 归档任务 #${taskId} 执行完成
                </pre>
            </div>
        </div>
    `;
    
    modal.style.display = 'block';
}

function hideTaskDetailsModal() {
    document.getElementById('taskDetailsModal').style.display = 'none';
}

function retryTask(taskId) {
    if (confirm('确定要重试此任务吗？')) {
        // 调用API重试任务
        alert('重试功能待实现');
    }
}

function stopTask(taskId) {
    if (confirm('确定要停止此任务吗？')) {
        // 调用API停止任务
        alert('停止功能待实现');
    }
}

function showError(errorMessage) {
    alert('错误信息:\n\n' + errorMessage);
}

// 自动刷新（每30秒）
setInterval(() => {
    const hasRunningTasks = document.querySelector('tr[data-status="running"]');
    if (hasRunningTasks) {
        location.reload();
    }
}, 30000);
</script>
{% endblock %} 