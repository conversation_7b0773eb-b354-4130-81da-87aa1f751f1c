#!/bin/sh 
. $HOME/.bash_profile
> /home/<USER>/dba_cmd1/all.log
/home/<USER>/dba_cmd1/time_12month.ksh > /home/<USER>/dba_cmd1/ttime_12month.tmp
/home/<USER>/dba_cmd1/select_count.ksh
cat /home/<USER>/dba_cmd1/count.tmp |grep aa >/home/<USER>/dba_cmd1/count.sql
/home/<USER>/dba_cmd1/select_row.ksh > /home/<USER>/dba_cmd1/select_row.tmp
cat /home/<USER>/dba_cmd1/select_row.tmp >>/home/<USER>/dba_cmd1/all.log
#时间
T_YEAR=cat /home/<USER>/dba_cmd1/ttime_12month.tmp |grep aa | awk '{print $2}'    #year:2010
T_MONTH=cat /home/<USER>/dba_cmd1/ttime_12month.tmp |grep bb | awk '{print $2}'   #month:03
T_DAY=cat /home/<USER>/dba_cmd1/ttime_12month.tmp |grep zz | awk '{print $2}'     #DAY:29
T_DATE=cat /home/<USER>/dba_cmd1/ttime_12month.tmp |grep dd | awk '{print $2}'    #date:20100329
#读取表名，写入时间戳作为文件名
INI=/home/<USER>/dba_cmd1/table_12month.ini
HOSTS=cat $INI |awk '{print $1}'                                         #table need to archive
DATE=date +%Y%m%d-%H%M%S
#数据循环处理，导出数据
for HOST in $HOSTS
do
   echo "====="  $HOST "===="
   case $HOST in
        PRE_ERP_BACK_MESS)
        exp PNBVWDA1/pnbvwadm123 TABLES=$HOST query=\"WHERE NOTE_TIME \<TO_DATE\(\'$T_YEAR\/$T_MONTH\/$T_DAY\'\,\'YYYY\/MM\/DD\'\)\"  file=/BK/dbpump/PNBVWDA1/"$HOST"-$T_DATE-$DATE.dmp log=/BK/dbpump/PNBVWDA1/"$HOST"-$T_DATE-$DATE.log indexes=n grants=n statistics=NONE
        cat /BK/dbpump/PNBVWDA1/"$HOST"-$T_DATE-$DATE.log >> /home/<USER>/dba_cmd1/all.log
        ;;
        PRE_ERP_ITEM_MOVE)
        exp PNBVWDA1/pnbvwadm123 TABLES=$HOST query=\"WHERE MOVE_DATE \<TO_DATE\(\'$T_YEAR\/$T_MONTH\/$T_DAY\'\,\'YYYY\/MM\/DD\'\)\"  file=/BK/dbpump/PNBVWDA1/"$HOST"-$T_DATE-$DATE.dmp log=/BK/dbpump/PNBVWDA1/"$HOST"-$T_DATE-$DATE.log indexes=n grants=n statistics=NONE
        cat /BK/dbpump/PNBVWDA1/"$HOST"-$T_DATE-$DATE.log >> /home/<USER>/dba_cmd1/all.log
        ;;
   esac
done

#初始化文件
> /home/<USER>/dba_cmd1/error.log
> /home/<USER>/dba_cmd1/drop.sql
> /home/<USER>/dba_cmd1/drop.tmp
> /home/<USER>/dba_cmd1/analyze.sql
> /home/<USER>/dba_cmd1/analyze.tmp

#循环批量删除，避免长时间等待
cat $INI |awk '{print $1}' | while read HOST
 do
  cat /home/<USER>/dba_cmd1/select_row.tmp |grep aa |awk '{print $2,$3}' | while read tname rown
    do
         if [[ "$tname" == "$HOST" ]] ;then
            cat /BK/dbpump/PNBVWDA1/"$HOST"-$T_DATE-$DATE.log | grep exporting | awk '{print $6}' | while read expn
              do
                if [ "$expn" == "$rown" ];then
                 if [ "$expn" -ne "0" ] ; then
                  echo "BEGIN" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "   loop" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "     DELETE FROM PNBVWDA1.$tname " >> /home/<USER>/dba_cmd1/drop.sql
                  case $tname in
                      PRE_ERP_BACK_MESS)
                      echo "    WHERE NOTE_TIME < TO_DATE('$T_YEAR/$T_MONTH/$T_DAY','yyyy/mm/dd')" >> /home/<USER>/dba_cmd1/drop.sql
                      ;;
                      PRE_ERP_ITEM_MOVE)
                      echo "    WHERE MOVE_DATE < TO_DATE('$T_YEAR/$T_MONTH/$T_DAY','yyyy/mm/dd')" >> /home/<USER>/dba_cmd1/drop.sql
                      ;;
                  esac
                  echo "            AND   ROWNUM < 5000;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "     IF SQL%NOTFOUND THEN" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "        EXIT;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "     END IF;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "     COMMIT;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "   END LOOP;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "   COMMIT;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "   " >> /home/<USER>/dba_cmd1/drop.sql
                  echo "   EXCEPTION" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "     WHEN NO_DATA_FOUND THEN" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "       NULL;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "     WHEN OTHERS THEN" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "       RAISE;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "END;" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "/" >> /home/<USER>/dba_cmd1/drop.sql
                  echo "EXEC DBMS_STATS.GATHER_TABLE_STATS(OWNNAME =>'PNBVWDA1',TABNAME =>'$tname',ESTIMATE_PERCENT =>10,GRANULARITY =>'DEFAULT',CASCADE =>TRUE);" >> /home/<USER>/dba_cmd1/analyze.sql
                 fi
                else
                  echo "$tname"_"$T_DATE"_"fail" >> /home/<USER>/dba_cmd1/error.log
                fi
              done
     fi     done
done
#压缩文件
cd /BK/dbpump/PNBVWDA1/
tar -czvf "PNBVWDA1-$T_DATE"-$DATE.tar.gz *$DATE.*
rm -f *$DATE.dmp
cp "PNBVWDA1-$T_DATE"-$DATE.tar.gz /BK/dbpump/nbubackup/
#执行删除和收集统计
/home/<USER>/dba_cmd1/drop.ksh >/home/<USER>/dba_cmd1/drop.tmp
/home/<USER>/dba_cmd1/analyze.ksh > /home/<USER>/dba_cmd1/analyze.tmp
cat /home/<USER>/dba_cmd1/drop.tmp >> /home/<USER>/dba_cmd1/all.log
cat /home/<USER>/dba_cmd1/analyze.tmp >> /home/<USER>/dba_cmd1/all.log
cat /home/<USER>/dba_cmd1/error.log >> /home/<USER>/dba_cmd1/all.log
#发送邮件
mailx -s "$ORACLE_SID PNBVWDA1_19c archive_18month ARCHIVE CHECK MAIL"  <EMAIL> < /home/<USER>/dba_cmd1/all.log  -- -f <EMAIL>