from flask import request, jsonify, current_app
from app import db
from app.models import DatabaseConnection, SystemLog
from app.api import api_bp
from app.utils.crypto import get_encryption_key

@api_bp.route('/database-connections', methods=['GET'])
def get_database_connections():
    """获取数据库连接列表"""
    try:
        connections = DatabaseConnection.query.all()
        return jsonify({
            'success': True,
            'data': [conn.to_dict() for conn in connections]
        })
    except Exception as e:
        SystemLog.log_error('database_connections', f'获取数据库连接列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取数据库连接列表失败: {str(e)}'
        }), 500

@api_bp.route('/database-connections', methods=['POST'])
def create_database_connection():
    """创建数据库连接"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'host', 'port', 'database', 'username', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查名称是否已存在
        existing = DatabaseConnection.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({
                'success': False,
                'message': '连接名称已存在'
            }), 400
        
        # 创建新连接
        connection = DatabaseConnection(
            name=data['name'],
            db_type=data.get('db_type', 'oracle'),
            host=data['host'],
            port=int(data['port']),
            database=data['database'],
            username=data['username'],
            is_active=data.get('is_active', True)
        )
        
        # 加密密码
        encryption_key = get_encryption_key()
        connection.set_password(data['password'], encryption_key)
        
        db.session.add(connection)
        db.session.commit()
        
        SystemLog.log_info('database_connections', f'创建数据库连接: {connection.name}')
        
        return jsonify({
            'success': True,
            'message': '数据库连接创建成功',
            'data': connection.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('database_connections', f'创建数据库连接失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'创建数据库连接失败: {str(e)}'
        }), 500

@api_bp.route('/database-connections/<int:connection_id>', methods=['PUT'])
def update_database_connection(connection_id):
    """更新数据库连接"""
    try:
        connection = DatabaseConnection.query.get_or_404(connection_id)
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            # 检查名称是否已被其他连接使用
            existing = DatabaseConnection.query.filter(
                DatabaseConnection.name == data['name'],
                DatabaseConnection.id != connection_id
            ).first()
            if existing:
                return jsonify({
                    'success': False,
                    'message': '连接名称已存在'
                }), 400
            connection.name = data['name']
        
        if 'db_type' in data:
            connection.db_type = data['db_type']
        if 'host' in data:
            connection.host = data['host']
        if 'port' in data:
            connection.port = int(data['port'])
        if 'database' in data:
            connection.database = data['database']
        if 'username' in data:
            connection.username = data['username']
        if 'is_active' in data:
            connection.is_active = data['is_active']
        
        # 如果提供了新密码，则更新密码
        if 'password' in data and data['password']:
            encryption_key = get_encryption_key()
            connection.set_password(data['password'], encryption_key)
        
        db.session.commit()
        
        SystemLog.log_info('database_connections', f'更新数据库连接: {connection.name}')
        
        return jsonify({
            'success': True,
            'message': '数据库连接更新成功',
            'data': connection.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('database_connections', f'更新数据库连接失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'更新数据库连接失败: {str(e)}'
        }), 500

@api_bp.route('/database-connections/<int:connection_id>', methods=['DELETE'])
def delete_database_connection(connection_id):
    """删除数据库连接"""
    try:
        connection = DatabaseConnection.query.get_or_404(connection_id)
        
        # 检查是否有关联的归档任务
        if connection.archive_tasks:
            return jsonify({
                'success': False,
                'message': '无法删除，该连接下还有归档任务'
            }), 400
        
        connection_name = connection.name
        db.session.delete(connection)
        db.session.commit()
        
        SystemLog.log_info('database_connections', f'删除数据库连接: {connection_name}')
        
        return jsonify({
            'success': True,
            'message': '数据库连接删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('database_connections', f'删除数据库连接失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除数据库连接失败: {str(e)}'
        }), 500

@api_bp.route('/database-connections/<int:connection_id>/test', methods=['POST'])
def test_database_connection(connection_id):
    """测试数据库连接"""
    try:
        connection = DatabaseConnection.query.get_or_404(connection_id)
        encryption_key = get_encryption_key()
        
        success, message = connection.test_connection(encryption_key)
        
        SystemLog.log_info('database_connections', f'测试数据库连接: {connection.name} - {message}')
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        SystemLog.log_error('database_connections', f'测试数据库连接失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'测试数据库连接失败: {str(e)}'
        }), 500
