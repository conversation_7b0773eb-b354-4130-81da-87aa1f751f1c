from app import db
from datetime import datetime
from cryptography.fernet import Fernet
import base64
import json

class UploadConfig(db.Model):
    """上传配置模型"""
    __tablename__ = 'upload_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.<PERSON>('archive_tasks.id'), nullable=False, comment='任务ID')
    
    # 上传配置
    upload_type = db.Column(db.String(20), nullable=False, comment='上传类型')  # local, sftp, ftp
    is_default = db.Column(db.<PERSON>, default=False, comment='是否为默认配置')
    
    # 服务器配置
    host = db.Column(db.String(255), comment='主机地址')
    port = db.Column(db.Integer, comment='端口号')
    username = db.Column(db.String(100), comment='用户名')
    password_encrypted = db.Column(db.Text, comment='加密后的密码')
    
    # 路径配置
    remote_path = db.Column(db.String(500), comment='远程路径')
    local_path = db.Column(db.String(500), comment='本地路径')
    
    # FTP特殊配置
    passive_mode = db.Column(db.Boolean, default=True, comment='FTP被动模式')
    
    # 其他配置
    config_json = db.Column(db.Text, comment='其他配置JSON')
    
    # 状态
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<UploadConfig {self.upload_type}:{self.host}>'
    
    def set_password(self, password, encryption_key):
        """加密并设置密码"""
        if password:
            fernet = Fernet(encryption_key.encode())
            encrypted_password = fernet.encrypt(password.encode())
            self.password_encrypted = base64.b64encode(encrypted_password).decode()
        else:
            self.password_encrypted = None
    
    def get_password(self, encryption_key):
        """解密并获取密码"""
        if not self.password_encrypted:
            return None
        try:
            fernet = Fernet(encryption_key.encode())
            encrypted_password = base64.b64decode(self.password_encrypted.encode())
            return fernet.decrypt(encrypted_password).decode()
        except Exception as e:
            raise ValueError(f"密码解密失败: {str(e)}")
    
    def get_config(self):
        """获取额外配置"""
        if self.config_json:
            try:
                return json.loads(self.config_json)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_config(self, config_dict):
        """设置额外配置"""
        if config_dict:
            self.config_json = json.dumps(config_dict, ensure_ascii=False)
        else:
            self.config_json = None
    
    def test_connection(self, encryption_key):
        """测试连接"""
        try:
            if self.upload_type == 'sftp':
                return self._test_sftp_connection(encryption_key)
            elif self.upload_type == 'ftp':
                return self._test_ftp_connection(encryption_key)
            elif self.upload_type == 'local':
                return self._test_local_path()
            else:
                return False, f"不支持的上传类型: {self.upload_type}"
        except Exception as e:
            return False, f"连接测试失败: {str(e)}"
    
    def _test_sftp_connection(self, encryption_key):
        """测试SFTP连接"""
        import paramiko
        
        password = self.get_password(encryption_key)
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            ssh.connect(
                hostname=self.host,
                port=self.port or 22,
                username=self.username,
                password=password,
                timeout=10
            )
            
            sftp = ssh.open_sftp()
            # 尝试列出远程目录
            sftp.listdir(self.remote_path or '.')
            sftp.close()
            ssh.close()
            
            return True, "SFTP连接成功"
        except Exception as e:
            return False, f"SFTP连接失败: {str(e)}"
    
    def _test_ftp_connection(self, encryption_key):
        """测试FTP连接"""
        import ftplib
        
        password = self.get_password(encryption_key)
        
        try:
            ftp = ftplib.FTP()
            ftp.connect(self.host, self.port or 21, timeout=10)
            ftp.login(self.username, password)
            
            if self.passive_mode:
                ftp.set_pasv(True)
            else:
                ftp.set_pasv(False)
            
            # 尝试切换到远程目录
            if self.remote_path:
                ftp.cwd(self.remote_path)
            
            ftp.quit()
            return True, "FTP连接成功"
        except Exception as e:
            return False, f"FTP连接失败: {str(e)}"
    
    def _test_local_path(self):
        """测试本地路径"""
        import os
        
        path = self.local_path or self.remote_path
        if not path:
            return False, "未配置本地路径"
        
        try:
            if not os.path.exists(path):
                os.makedirs(path, exist_ok=True)
            
            # 测试写入权限
            test_file = os.path.join(path, '.test_write')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            
            return True, "本地路径可用"
        except Exception as e:
            return False, f"本地路径测试失败: {str(e)}"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'upload_type': self.upload_type,
            'is_default': self.is_default,
            'host': self.host,
            'port': self.port,
            'username': self.username,
            'remote_path': self.remote_path,
            'local_path': self.local_path,
            'passive_mode': self.passive_mode,
            'config': self.get_config(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
