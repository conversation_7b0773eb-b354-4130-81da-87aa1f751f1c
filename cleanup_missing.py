#!/usr/bin/env python3
"""
清理不存在文件工具
用于清理服务器上已经不存在但仍在目录中显示的文件引用
"""

import asyncio
import aiohttp
import yaml
import sys
import logging
import json
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


class CleanupClient:
    """清理客户端"""
    
    def __init__(self, config):
        self.config = config
        self.base_url = config['openlist']['base_url'].rstrip('/')
        self.username = config['openlist'].get('username', '')
        self.password = config['openlist'].get('password', '')
        self.token = config['openlist'].get('token', '')
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        await self.login()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    async def login(self):
        """登录获取token"""
        if self.token:
            logger.info("使用配置的token")
            return
            
        if not self.username:
            logger.info("未配置用户名和token，使用匿名访问")
            return
            
        login_url = f"{self.base_url}/api/auth/login"
        login_data = {"username": self.username, "password": self.password}
        
        async with self.session.post(login_url, json=login_data) as resp:
            if resp.status == 200:
                data = await resp.json()
                self.token = data.get('data', {}).get('token', '')
                if self.token:
                    logger.info("登录成功")
                else:
                    logger.error("登录响应中未找到token")
            else:
                text = await resp.text()
                logger.error(f"登录失败: {resp.status}, {text}")
                
    def _get_headers(self):
        """获取请求头"""
        headers = {}
        if self.token:
            headers['Authorization'] = self.token
        return headers
        
    async def list_dir(self, path: str, password: str = ""):
        """列出目录内容"""
        list_url = f"{self.base_url}/api/fs/list"
        data = {"path": path, "password": password, "page": 1, "per_page": 0, "refresh": False}
        
        async with self.session.post(list_url, json=data, headers=self._get_headers()) as resp:
            if resp.status == 200:
                result = await resp.json()
                return result.get('data', {})
            else:
                logger.error(f"列出目录失败 {path}: HTTP {resp.status}")
                return {}
                
    async def get_file_info(self, path: str, password: str = ""):
        """获取文件信息"""
        get_url = f"{self.base_url}/api/fs/get"
        data = {"path": path, "password": password}
        
        async with self.session.post(get_url, json=data, headers=self._get_headers()) as resp:
            if resp.status == 200:
                result = await resp.json()
                return result.get('data', {})
            else:
                logger.error(f"获取文件信息失败 {path}: {resp.status}")
                return {}
                
    async def check_file_exists(self, path: str, password: str = ""):
        """检查文件是否存在（通过尝试获取下载链接）"""
        file_info = await self.get_file_info(path, password)
        if not file_info:
            return False
            
        raw_url = file_info.get('raw_url', '')
        if not raw_url:
            return False
            
        # 尝试HEAD请求检查文件是否存在
        try:
            async with self.session.get(raw_url, headers=self._get_headers()) as resp:
                if resp.status == 200:
                    content_type = resp.headers.get('content-type', '').lower()
                    if content_type == 'application/json':
                        # 可能是错误响应，读取内容检查
                        content = await resp.read()
                        try:
                            error_data = json.loads(content.decode('utf-8'))
                            error_message = error_data.get('message', '')
                            if 'object not found' in error_message or 'file not found' in error_message:
                                return False
                        except:
                            pass
                    return True
                else:
                    return False
        except Exception as e:
            logger.debug(f"检查文件存在性时出错 {path}: {e}")
            return False
            
    async def scan_missing_files(self, root_path: str, password: str = ""):
        """扫描目录中不存在的文件"""
        missing_files = []
        total_files = 0
        
        logger.info(f"开始扫描目录: {root_path}")
        
        async def scan_directory(path: str, depth: int = 0):
            nonlocal total_files
            
            if depth > 10:  # 防止无限递归
                return
                
            dir_data = await self.list_dir(path, password)
            if not dir_data:
                return
                
            content = dir_data.get('content', [])
            if not content:
                return
                
            for item in content:
                if not item or not isinstance(item, dict):
                    continue
                    
                item_name = item.get('name', '')
                if not item_name:
                    continue
                    
                item_path = f"{path.rstrip('/')}/{item_name}"
                
                if item.get('is_dir', False):
                    # 递归处理子目录
                    await scan_directory(item_path, depth + 1)
                else:
                    # 检查文件
                    file_ext = Path(item_name).suffix.lower()
                    if file_ext in {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.nfo', '.srt', '.ass', '.vtt'}:
                        total_files += 1
                        logger.info(f"检查文件 [{total_files}]: {item_path}")
                        
                        exists = await self.check_file_exists(item_path, password)
                        if not exists:
                            missing_files.append(item_path)
                            logger.warning(f"文件不存在: {item_path}")
        
        await scan_directory(root_path)
        
        logger.info("=" * 60)
        logger.info(f"扫描完成:")
        logger.info(f"  总文件数: {total_files}")
        logger.info(f"  不存在文件数: {len(missing_files)}")
        logger.info("=" * 60)
        
        if missing_files:
            logger.info("不存在的文件列表:")
            for i, file_path in enumerate(missing_files, 1):
                logger.info(f"  {i:3d}. {file_path}")
                
        return missing_files


async def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python cleanup_missing.py <config_file> <remote_path> [password]")
        print("示例: python cleanup_missing.py config.yaml '/123/EMBY/Movies'")
        return
        
    config_file = sys.argv[1]
    remote_path = sys.argv[2]
    password = sys.argv[3] if len(sys.argv) > 3 else ""
    
    # 加载配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return
        
    # 开始扫描
    async with CleanupClient(config) as client:
        missing_files = await client.scan_missing_files(remote_path, password)
        
        if missing_files:
            logger.info(f"\n发现 {len(missing_files)} 个不存在的文件")
            logger.info("您可以将这些信息提供给服务器管理员以清理目录索引")
        else:
            logger.info("未发现不存在的文件")


if __name__ == '__main__':
    asyncio.run(main()) 