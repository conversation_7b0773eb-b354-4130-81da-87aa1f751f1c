#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库归档管理平台安装脚本
"""

import os
import sys
import subprocess
import yaml
from cryptography.fernet import <PERSON><PERSON><PERSON>

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本检查通过: {sys.version}")

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ 依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"错误: 依赖包安装失败: {e}")
        sys.exit(1)

def create_directories():
    """创建必要的目录"""
    directories = ['data', 'logs', 'temp', 'backups', 'static/css', 'static/js', 'static/images']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"✓ 创建目录: {directory}")

def generate_security_keys():
    """生成安全密钥"""
    # 生成加密密钥
    encryption_key = Fernet.generate_key().decode()
    
    # 生成应用密钥
    import secrets
    secret_key = secrets.token_urlsafe(32)
    
    return encryption_key, secret_key

def update_config():
    """更新配置文件"""
    config_path = 'config/archive_config.yaml'
    
    if not os.path.exists(config_path):
        print("错误: 配置文件不存在")
        return
    
    # 读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 生成新的密钥
    encryption_key, secret_key = generate_security_keys()
    
    # 更新配置
    config['app']['secret_key'] = secret_key
    config['security']['encryption_key'] = encryption_key
    
    # 写回配置文件
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print("✓ 配置文件更新完成")
    print(f"  - 应用密钥: {secret_key[:20]}...")
    print(f"  - 加密密钥: {encryption_key[:20]}...")

def initialize_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    try:
        from app import create_app, db
        
        app = create_app()
        with app.app_context():
            db.create_all()
            print("✓ 数据库初始化完成")
    except Exception as e:
        print(f"错误: 数据库初始化失败: {e}")
        sys.exit(1)

def create_sample_data():
    """创建示例数据"""
    try:
        from app import create_app, db
        from app.models import DatabaseConnection, ArchiveTask
        from app.utils.crypto import get_encryption_key
        
        app = create_app()
        with app.app_context():
            # 检查是否已有数据
            if DatabaseConnection.query.first():
                print("✓ 数据库中已有数据，跳过示例数据创建")
                return
            
            # 创建示例数据库连接
            encryption_key = get_encryption_key()
            
            sample_conn = DatabaseConnection(
                name="示例Oracle连接",
                db_type="oracle",
                host="localhost",
                port=1521,
                database="XE",
                username="sample_user",
                is_active=False  # 默认不启用
            )
            sample_conn.set_password("sample_password", encryption_key)
            
            db.session.add(sample_conn)
            db.session.commit()
            
            # 创建示例归档任务
            sample_task = ArchiveTask(
                name="示例归档任务",
                description="这是一个示例归档任务，用于演示系统功能",
                database_id=sample_conn.id,
                table_name="SAMPLE_TABLE",
                date_field="CREATE_DATE",
                compression_enabled=True,
                delete_after_archive=False,  # 示例任务不删除数据
                is_active=False  # 默认不启用
            )
            
            db.session.add(sample_task)
            db.session.commit()
            
            print("✓ 示例数据创建完成")
            
    except Exception as e:
        print(f"警告: 示例数据创建失败: {e}")

def print_next_steps():
    """打印后续步骤"""
    print("\n" + "="*50)
    print("安装完成！")
    print("="*50)
    print("\n后续步骤:")
    print("1. 编辑配置文件: config/archive_config.yaml")
    print("2. 配置数据库连接信息")
    print("3. 配置邮件和通知设置")
    print("4. 运行应用: python run.py")
    print("5. 访问 http://localhost:8080")
    print("\n注意事项:")
    print("- 请妥善保管配置文件中的密钥")
    print("- 生产环境请修改默认端口和密钥")
    print("- 建议定期备份数据库文件")

def main():
    """主函数"""
    print("数据库归档管理平台安装程序")
    print("="*40)
    
    # 检查Python版本
    check_python_version()
    
    # 安装依赖
    install_dependencies()
    
    # 创建目录
    create_directories()
    
    # 更新配置
    update_config()
    
    # 初始化数据库
    initialize_database()
    
    # 创建示例数据
    create_sample_data()
    
    # 打印后续步骤
    print_next_steps()

if __name__ == '__main__':
    main()
