from app import db
from datetime import datetime
import json

class NotificationConfig(db.Model):
    """通知配置模型"""
    __tablename__ = 'notification_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 通知类型
    type = db.Column(db.String(20), nullable=False, comment='通知类型')  # email, wechat, dingtalk
    name = db.Column(db.String(100), nullable=False, comment='配置名称')
    
    # 配置内容
    config_json = db.Column(db.Text, nullable=False, comment='配置JSON')
    
    # 通知规则
    notify_on_success = db.Column(db.<PERSON><PERSON>, default=True, comment='成功时通知')
    notify_on_failure = db.Column(db.<PERSON>, default=True, comment='失败时通知')
    notify_on_warning = db.Column(db.<PERSON>, default=True, comment='警告时通知')
    
    # 状态
    is_active = db.Column(db.<PERSON>, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<NotificationConfig {self.type}:{self.name}>'
    
    def get_config(self):
        """获取配置"""
        try:
            return json.loads(self.config_json)
        except json.JSONDecodeError:
            return {}
    
    def set_config(self, config_dict):
        """设置配置"""
        self.config_json = json.dumps(config_dict, ensure_ascii=False)
    
    def test_notification(self):
        """测试通知配置"""
        try:
            config = self.get_config()
            
            if self.type == 'email':
                return self._test_email(config)
            elif self.type == 'wechat':
                return self._test_wechat(config)
            elif self.type == 'dingtalk':
                return self._test_dingtalk(config)
            else:
                return False, f"不支持的通知类型: {self.type}"
                
        except Exception as e:
            return False, f"通知测试失败: {str(e)}"
    
    def _test_email(self, config):
        """测试邮件配置"""
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        
        try:
            # 创建SMTP连接
            if config.get('use_tls', True):
                server = smtplib.SMTP(config['smtp_server'], config.get('smtp_port', 587))
                server.starttls()
            else:
                server = smtplib.SMTP(config['smtp_server'], config.get('smtp_port', 25))
            
            # 登录
            if config.get('username') and config.get('password'):
                server.login(config['username'], config['password'])
            
            server.quit()
            return True, "邮件配置测试成功"
            
        except Exception as e:
            return False, f"邮件配置测试失败: {str(e)}"
    
    def _test_wechat(self, config):
        """测试企业微信配置"""
        import requests
        
        try:
            webhook_url = config.get('webhook_url')
            if not webhook_url:
                return False, "未配置企业微信Webhook URL"
            
            # 发送测试消息
            data = {
                "msgtype": "text",
                "text": {
                    "content": "数据库归档管理平台通知配置测试"
                }
            }
            
            response = requests.post(webhook_url, json=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('errcode') == 0:
                return True, "企业微信配置测试成功"
            else:
                return False, f"企业微信配置测试失败: {result.get('errmsg', '未知错误')}"
                
        except Exception as e:
            return False, f"企业微信配置测试失败: {str(e)}"
    
    def _test_dingtalk(self, config):
        """测试钉钉配置"""
        import requests
        import time
        import hmac
        import hashlib
        import base64
        import urllib.parse
        
        try:
            webhook_url = config.get('webhook_url')
            secret = config.get('secret')
            
            if not webhook_url:
                return False, "未配置钉钉Webhook URL"
            
            # 如果有密钥，生成签名
            if secret:
                timestamp = str(round(time.time() * 1000))
                secret_enc = secret.encode('utf-8')
                string_to_sign = '{}\n{}'.format(timestamp, secret)
                string_to_sign_enc = string_to_sign.encode('utf-8')
                hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
                webhook_url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
            
            # 发送测试消息
            data = {
                "msgtype": "text",
                "text": {
                    "content": "数据库归档管理平台通知配置测试"
                }
            }
            
            response = requests.post(webhook_url, json=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('errcode') == 0:
                return True, "钉钉配置测试成功"
            else:
                return False, f"钉钉配置测试失败: {result.get('errmsg', '未知错误')}"
                
        except Exception as e:
            return False, f"钉钉配置测试失败: {str(e)}"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'type': self.type,
            'name': self.name,
            'config': self.get_config(),
            'notify_on_success': self.notify_on_success,
            'notify_on_failure': self.notify_on_failure,
            'notify_on_warning': self.notify_on_warning,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
