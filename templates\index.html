{% extends "base.html" %}

{% block title %}首页 - 数据库归档管理平台{% endblock %}

{% block page_title %}仪表板{% endblock %}

{% block content %}
<!-- 统计卡片行 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="stats-icon">
                <i class="fas fa-database"></i>
            </div>
            <div class="stats-number" id="total-connections">0</div>
            <div class="stats-label">数据库连接</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-icon">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stats-number" id="total-tasks">0</div>
            <div class="stats-label">归档任务</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="stats-icon">
                <i class="fas fa-play-circle"></i>
            </div>
            <div class="stats-number" id="running-tasks">0</div>
            <div class="stats-label">运行中任务</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="stats-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number" id="today-executions">0</div>
            <div class="stats-label">今日执行</div>
        </div>
    </div>
</div>

<!-- 快速操作和系统状态 -->
<div class="row">
    <!-- 快速操作 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('database_connections') }}" class="btn btn-primary btn-lg w-100 btn-icon">
                            <i class="fas fa-plug"></i>
                            <span>数据库连接管理</span>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('archive_tasks') }}" class="btn btn-success btn-lg w-100 btn-icon">
                            <i class="fas fa-tasks"></i>
                            <span>归档任务管理</span>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('execution_history') }}" class="btn btn-info btn-lg w-100 btn-icon">
                            <i class="fas fa-history"></i>
                            <span>执行历史</span>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('system_settings') }}" class="btn btn-warning btn-lg w-100 btn-icon">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统状态 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>系统状态
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>数据库连接</span>
                    <span class="status-badge status-success">
                        <i class="fas fa-check-circle"></i>正常
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>调度器状态</span>
                    <span class="status-badge status-success" id="scheduler-status">
                        <i class="fas fa-check-circle"></i>运行中
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>系统运行时间</span>
                    <span class="text-muted" id="system-uptime">刚启动</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>内存使用</span>
                    <span class="text-muted" id="memory-usage">--</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近执行和活动日志 -->
<div class="row">
    <!-- 最近执行 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>最近执行
                </h5>
                <a href="{{ url_for('execution_history') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                <div id="recent-executions">
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>暂无执行记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统日志 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>系统日志
                </h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="refreshSystemLogs()">
                    <i class="fas fa-sync-alt"></i>刷新
                </button>
            </div>
            <div class="card-body">
                <div id="system-logs" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <p>正在加载日志...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API测试区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-code me-2"></i>API测试
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="testAPI('/api/database-connections')">
                            <i class="fas fa-plug me-2"></i>数据库连接API
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="testAPI('/api/archive-tasks')">
                            <i class="fas fa-tasks me-2"></i>归档任务API
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="testAPI('/api/dashboard/overview')">
                            <i class="fas fa-chart-bar me-2"></i>仪表板API
                        </button>
                    </div>
                </div>
                <div id="api-test-result" class="mt-3" style="display: none;">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6>API测试结果:</h6>
                            <pre id="api-result-content" class="mb-0" style="max-height: 200px; overflow-y: auto;"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    loadRecentExecutions();
    loadSystemLogs();
    
    // 每30秒刷新一次数据
    setInterval(loadDashboardData, 30000);
});

// 加载仪表板数据
async function loadDashboardData() {
    try {
        const response = await axios.get('/api/dashboard/overview');
        const data = response.data.data;
        
        document.getElementById('total-connections').textContent = data.total_connections || 0;
        document.getElementById('total-tasks').textContent = data.total_tasks || 0;
        document.getElementById('running-tasks').textContent = data.running_tasks || 0;
        document.getElementById('today-executions').textContent = data.today_executions || 0;
        
        // 更新系统状态
        if (data.system_status) {
            updateSystemStatus(data.system_status);
        }
    } catch (error) {
        console.error('加载仪表板数据失败:', error);
    }
}

// 更新系统状态
function updateSystemStatus(status) {
    const schedulerStatus = document.getElementById('scheduler-status');
    const systemUptime = document.getElementById('system-uptime');
    const memoryUsage = document.getElementById('memory-usage');
    
    if (status.scheduler_running) {
        schedulerStatus.className = 'status-badge status-success';
        schedulerStatus.innerHTML = '<i class="fas fa-check-circle"></i>运行中';
    } else {
        schedulerStatus.className = 'status-badge status-error';
        schedulerStatus.innerHTML = '<i class="fas fa-times-circle"></i>已停止';
    }
    
    if (status.uptime) {
        systemUptime.textContent = status.uptime;
    }
    
    if (status.memory_usage) {
        memoryUsage.textContent = status.memory_usage;
    }
}

// 加载最近执行记录
async function loadRecentExecutions() {
    try {
        const response = await axios.get('/api/task-executions?limit=5');
        const executions = response.data.data;
        
        const container = document.getElementById('recent-executions');
        
        if (executions.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>暂无执行记录</p>
                </div>
            `;
            return;
        }
        
        const html = executions.map(execution => `
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div>
                    <div class="fw-bold">${execution.task_name || '未知任务'}</div>
                    <small class="text-muted">${formatDateTime(execution.start_time)}</small>
                </div>
                <span class="status-badge status-${execution.status === 'success' ? 'success' : execution.status === 'failed' ? 'error' : 'warning'}">
                    ${execution.status}
                </span>
            </div>
        `).join('');
        
        container.innerHTML = html;
    } catch (error) {
        console.error('加载最近执行记录失败:', error);
    }
}

// 加载系统日志
async function loadSystemLogs() {
    try {
        const response = await axios.get('/api/system-logs?limit=10');
        const logs = response.data.data;
        
        const container = document.getElementById('system-logs');
        
        if (logs.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <p>暂无日志记录</p>
                </div>
            `;
            return;
        }
        
        const html = logs.map(log => `
            <div class="d-flex align-items-start py-2 border-bottom">
                <span class="status-badge status-${log.level === 'ERROR' ? 'error' : log.level === 'WARNING' ? 'warning' : 'info'} me-2">
                    ${log.level}
                </span>
                <div class="flex-grow-1">
                    <div class="small">${log.message}</div>
                    <small class="text-muted">${formatDateTime(log.created_at)}</small>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    } catch (error) {
        console.error('加载系统日志失败:', error);
    }
}

// 刷新系统日志
function refreshSystemLogs() {
    loadSystemLogs();
}

// API测试
async function testAPI(endpoint) {
    const resultDiv = document.getElementById('api-test-result');
    const contentPre = document.getElementById('api-result-content');
    
    try {
        showLoading();
        const response = await axios.get(endpoint);
        
        contentPre.textContent = JSON.stringify(response.data, null, 2);
        resultDiv.style.display = 'block';
        
        showMessage('API测试成功', 'success');
    } catch (error) {
        contentPre.textContent = `错误: ${error.message}`;
        resultDiv.style.display = 'block';
        
        showMessage('API测试失败', 'error');
    } finally {
        hideLoading();
    }
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '--';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}
</script>
{% endblock %}
