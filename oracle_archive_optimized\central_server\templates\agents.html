{% extends "base.html" %}

{% block title %}Agent管理 - Oracle 分布式归档系统{% endblock %}

{% block content %}
<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>Agent 管理</h2>
        <button class="btn btn-primary" onclick="showAddAgentModal()">添加 Agent</button>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Agent ID</th>
                <th>主机地址</th>
                <th>Oracle 实例</th>
                <th>状态</th>
                <th>最后心跳</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for agent in agents %}
            <tr>
                <td>{{ agent[0] }}</td>
                <td>{{ agent[1] }}</td>
                <td>{{ agent[2] }}:{{ agent[3] }}</td>
                <td>
                    <span class="status status-{{ agent[4] }}">{{ agent[4] }}</span>
                </td>
                <td>{{ agent[5] or '从未连接' }}</td>
                <td>
                    <button class="btn btn-sm" onclick="testAgent('{{ agent[0] }}')">测试</button>
                    <button class="btn btn-sm" onclick="viewAgentDetails('{{ agent[0] }}')">详情</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteAgent('{{ agent[0] }}')">删除</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    {% if not agents %}
    <p style="text-align: center; color: #666; padding: 2rem;">暂无Agent，请点击"添加Agent"注册新的Agent。</p>
    {% endif %}
</div>

<!-- 添加Agent模态框 -->
<div id="addAgentModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="background: white; margin: 5% auto; padding: 2rem; width: 600px; border-radius: 8px;">
        <h3>添加 Agent</h3>
        <form id="addAgentForm">
            <div style="margin-bottom: 1rem;">
                <label>Agent ID:</label>
                <input type="text" name="agent_id" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>主机地址:</label>
                <input type="text" name="host" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>SSH 端口:</label>
                <input type="number" name="ssh_port" value="22" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>SSH 用户:</label>
                <input type="text" name="ssh_user" value="oracle" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>Oracle 主机:</label>
                <input type="text" name="oracle_host" value="localhost" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>Oracle 端口:</label>
                <input type="number" name="oracle_port" value="1521" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label>Oracle SID:</label>
                <input type="text" name="oracle_sid" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" class="btn" onclick="hideAddAgentModal()">取消</button>
                <button type="submit" class="btn btn-primary">添加</button>
            </div>
        </form>
    </div>
</div>

<script>
function showAddAgentModal() {
    document.getElementById('addAgentModal').style.display = 'block';
}

function hideAddAgentModal() {
    document.getElementById('addAgentModal').style.display = 'none';
    document.getElementById('addAgentForm').reset();
}

document.getElementById('addAgentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {};
    formData.forEach((value, key) => data[key] = value);
    
    fetch('/api/agent/register', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('Agent 添加成功');
            location.reload();
        } else {
            alert('添加失败: ' + result.error);
        }
    });
});

function testAgent(agentId) {
    alert('测试 Agent: ' + agentId);
    // 这里可以添加实际的测试逻辑
}

function viewAgentDetails(agentId) {
    alert('查看 Agent 详情: ' + agentId);
    // 这里可以跳转到详情页面或显示模态框
}

function deleteAgent(agentId) {
    if (confirm('确定要删除 Agent: ' + agentId + ' 吗？')) {
        // 这里添加删除逻辑
        alert('删除功能待实现');
    }
}

// 定时刷新页面
setTimeout(() => location.reload(), 30000);
</script>
{% endblock %} 