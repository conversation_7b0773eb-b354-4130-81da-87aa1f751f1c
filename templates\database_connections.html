{% extends "base.html" %}

{% block title %}数据库连接管理 - 数据库归档管理平台{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">数据库连接管理</li>
{% endblock %}

{% block page_title %}数据库连接管理{% endblock %}

{% block page_actions %}
<button class="btn btn-primary btn-icon" onclick="showConnectionDialog()">
    <i class="fas fa-plus"></i>
    <span>添加连接</span>
</button>
{% endblock %}

{% block content %}
<!-- 连接统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-database fa-2x text-primary mb-2"></i>
                <h4 class="mb-0" id="total-connections">0</h4>
                <small class="text-muted">总连接数</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4 class="mb-0" id="active-connections">0</h4>
                <small class="text-muted">活跃连接</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                <h4 class="mb-0" id="inactive-connections">0</h4>
                <small class="text-muted">禁用连接</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-plug fa-2x text-info mb-2"></i>
                <h4 class="mb-0" id="oracle-connections">0</h4>
                <small class="text-muted">Oracle连接</small>
            </div>
        </div>
    </div>
</div>

<!-- 连接列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>连接列表
        </h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-secondary" onclick="refreshConnections()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="btn btn-sm btn-outline-info" onclick="testAllConnections()">
                <i class="fas fa-check-double"></i> 测试全部
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>连接名称</th>
                        <th>数据库类型</th>
                        <th>主机地址</th>
                        <th>数据库</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="connections-table-body">
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载连接列表...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 连接配置模态框 -->
<div class="modal fade" id="connectionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="connectionModalTitle">
                    <i class="fas fa-plus me-2"></i>添加数据库连接
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="connectionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">连接名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">为此连接指定一个易识别的名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="db_type" class="form-label">数据库类型</label>
                                <select class="form-select" id="db_type" name="db_type">
                                    <option value="oracle">Oracle</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="host" class="form-label">主机地址 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="host" name="host" required>
                                <div class="form-text">数据库服务器的IP地址或域名</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="port" class="form-label">端口号 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="port" name="port" value="1521" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="database" class="form-label">数据库名/服务名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="database" name="database" required>
                        <div class="form-text">Oracle数据库的服务名或SID</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                启用此连接
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" onclick="testConnection()">
                    <i class="fas fa-check"></i> 测试连接
                </button>
                <button type="button" class="btn btn-primary" onclick="saveConnection()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 连接详情模态框 -->
<div class="modal fade" id="connectionDetailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>连接详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="connectionDetailContent">
                <!-- 连接详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/database-connections.js') }}"></script>
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadConnections();
    updateConnectionStats();
});

// 显示连接配置对话框
function showConnectionDialog(connection = null) {
    const modal = new bootstrap.Modal(document.getElementById('connectionModal'));
    const form = document.getElementById('connectionForm');
    const title = document.getElementById('connectionModalTitle');
    
    if (connection) {
        // 编辑模式
        title.innerHTML = '<i class="fas fa-edit me-2"></i>编辑数据库连接';
        form.name.value = connection.name;
        form.db_type.value = connection.db_type;
        form.host.value = connection.host;
        form.port.value = connection.port;
        form.database.value = connection.database;
        form.username.value = connection.username;
        form.password.value = '';
        form.is_active.checked = connection.is_active;
        
        // 存储当前编辑的连接ID
        form.dataset.connectionId = connection.id;
    } else {
        // 新增模式
        title.innerHTML = '<i class="fas fa-plus me-2"></i>添加数据库连接';
        form.reset();
        form.db_type.value = 'oracle';
        form.port.value = '1521';
        form.is_active.checked = true;
        delete form.dataset.connectionId;
    }
    
    modal.show();
}

// 加载连接列表
async function loadConnections() {
    try {
        const response = await axios.get('/api/database-connections');
        const connections = response.data.data;
        
        renderConnectionsTable(connections);
        updateConnectionStats();
    } catch (error) {
        console.error('加载连接列表失败:', error);
        showMessage('加载连接列表失败: ' + error.message, 'error');
    }
}

// 渲染连接表格
function renderConnectionsTable(connections) {
    const tbody = document.getElementById('connections-table-body');
    
    if (connections.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无数据库连接</div>
                    <button class="btn btn-primary btn-sm mt-2" onclick="showConnectionDialog()">
                        <i class="fas fa-plus me-1"></i>添加第一个连接
                    </button>
                </td>
            </tr>
        `;
        return;
    }
    
    const html = connections.map(conn => `
        <tr>
            <td>
                <div class="fw-bold">${conn.name}</div>
                <small class="text-muted">ID: ${conn.id}</small>
            </td>
            <td>
                <span class="badge bg-primary">${conn.db_type.toUpperCase()}</span>
            </td>
            <td>${conn.host}:${conn.port}</td>
            <td>${conn.database}</td>
            <td>
                <span class="status-badge status-${conn.is_active ? 'success' : 'error'}">
                    <i class="fas fa-${conn.is_active ? 'check' : 'times'}-circle"></i>
                    ${conn.is_active ? '启用' : '禁用'}
                </span>
            </td>
            <td>${formatDateTime(conn.created_at)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="showConnectionDetail(${conn.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="testConnectionById(${conn.id})" title="测试连接">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-outline-primary" onclick="editConnection(${conn.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteConnection(${conn.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// 更新连接统计
async function updateConnectionStats() {
    try {
        const response = await axios.get('/api/database-connections');
        const connections = response.data.data;
        
        const total = connections.length;
        const active = connections.filter(c => c.is_active).length;
        const inactive = total - active;
        const oracle = connections.filter(c => c.db_type === 'oracle').length;
        
        document.getElementById('total-connections').textContent = total;
        document.getElementById('active-connections').textContent = active;
        document.getElementById('inactive-connections').textContent = inactive;
        document.getElementById('oracle-connections').textContent = oracle;
    } catch (error) {
        console.error('更新连接统计失败:', error);
    }
}

// 刷新连接列表
function refreshConnections() {
    loadConnections();
}

// 测试所有连接
async function testAllConnections() {
    try {
        showLoading();
        const response = await axios.get('/api/database-connections');
        const connections = response.data.data;
        
        let successCount = 0;
        let totalCount = connections.length;
        
        for (const conn of connections) {
            try {
                const testResponse = await axios.post(`/api/database-connections/${conn.id}/test`);
                if (testResponse.data.success) {
                    successCount++;
                }
            } catch (error) {
                console.error(`测试连接 ${conn.name} 失败:`, error);
            }
        }
        
        showMessage(`连接测试完成: ${successCount}/${totalCount} 个连接正常`, 'info');
    } catch (error) {
        showMessage('测试连接失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '--';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}
</script>
{% endblock %}
