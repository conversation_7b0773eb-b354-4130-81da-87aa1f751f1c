#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Oracle 分布式归档系统 - 中央控制服务（优化版）
支持通过SSH远程执行Shell脚本任务
"""

import os
import sys
import yaml
import json
import logging
import sqlite3
import threading
import paramiko
import time
from datetime import datetime, timedelta
from pathlib import Path
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
from werkzeug.utils import secure_filename
from typing import Dict, List, Optional, Tuple


class SSHManager:
    """SSH连接管理器"""
    
    def __init__(self, logger):
        self.logger = logger
        self.connections = {}
        
    def get_connection(self, host: str, port: int, username: str, key_file: str = None, password: str = None) -> paramiko.SSHClient:
        """获取SSH连接"""
        conn_key = f"{host}:{port}"
        
        if conn_key in self.connections:
            ssh = self.connections[conn_key]
            # 检查连接是否仍然有效
            try:
                ssh.exec_command("echo 'test'")
                return ssh
            except:
                del self.connections[conn_key]
        
        # 创建新连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            if key_file and os.path.exists(key_file):
                ssh.connect(host, port=port, username=username, key_filename=key_file)
            else:
                ssh.connect(host, port=port, username=username, password=password)
            
            self.connections[conn_key] = ssh
            self.logger.info(f"SSH连接成功: {conn_key}")
            return ssh
            
        except Exception as e:
            self.logger.error(f"SSH连接失败 {conn_key}: {e}")
            raise
    
    def close_all(self):
        """关闭所有连接"""
        for conn in self.connections.values():
            try:
                conn.close()
            except:
                pass
        self.connections.clear()


class CentralControlServer:
    """中央控制服务器（优化版）"""
    
    def __init__(self, config_path: str = "central_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.db_path = "central_control.db"
        self._init_database()
        self.logger = self._setup_logging()
        self.ssh_manager = SSHManager(self.logger)
        self._ensure_directories()
        
    def _load_config(self):
        """加载配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            # 创建默认配置
            default_config = {
                'server': {
                    'host': '0.0.0.0',
                    'port': 8080,
                    'debug': False
                },
                'storage': {
                    'type': 'local',
                    'local_path': './central_archives'
                },
                'ssh': {
                    'key_file': '~/.ssh/id_rsa',
                    'default_port': 22,
                    'timeout': 300
                },
                'scheduler': {
                    'enabled': True,
                    'check_interval': 60,
                    'task_timeout': 3600
                },
                'logging': {
                    'level': 'INFO',
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                }
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
                
            return default_config
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Agent管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_id TEXT UNIQUE NOT NULL,
                host TEXT NOT NULL,
                ssh_port INTEGER DEFAULT 22,
                ssh_user TEXT DEFAULT 'oracle',
                oracle_host TEXT NOT NULL,
                oracle_port INTEGER DEFAULT 1521,
                oracle_sid TEXT NOT NULL,
                status TEXT DEFAULT 'offline',
                last_heartbeat TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 归档表配置
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS archive_tables (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_id TEXT NOT NULL,
                table_name TEXT NOT NULL,
                schema_name TEXT NOT NULL,
                date_field TEXT NOT NULL,
                retention_days INTEGER NOT NULL,
                batch_size INTEGER DEFAULT 10000,
                enabled BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (agent_id) REFERENCES agents (agent_id)
            )
        ''')
        
        # 归档任务表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS archive_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_id TEXT NOT NULL,
                table_id INTEGER NOT NULL,
                status TEXT DEFAULT 'pending',
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                records_exported INTEGER DEFAULT 0,
                records_deleted INTEGER DEFAULT 0,
                file_path TEXT,
                file_size INTEGER DEFAULT 0,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (agent_id) REFERENCES agents (agent_id),
                FOREIGN KEY (table_id) REFERENCES archive_tables (id)
            )
        ''')
        
        # 定时计划表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS table_schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_id INTEGER UNIQUE NOT NULL,
                cron_expression TEXT NOT NULL,
                enabled BOOLEAN DEFAULT 1,
                last_run TIMESTAMP,
                next_run TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (table_id) REFERENCES archive_tables (id) ON DELETE CASCADE
            )
        ''')
        
        # SSH密钥管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ssh_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_id TEXT UNIQUE NOT NULL,
                private_key TEXT,
                public_key TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (agent_id) REFERENCES agents (agent_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _setup_logging(self):
        """设置日志"""
        log_dir = Path("./logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"central_control_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=getattr(logging, self.config['logging']['level']),
            format=self.config['logging']['format'],
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return logging.getLogger(__name__)
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        dirs = [
            self.config['storage']['local_path'],
            './logs',
            './temp'
        ]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(exist_ok=True)
    
    def register_agent(self, agent_data: dict) -> bool:
        """注册Agent"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO agents 
                (agent_id, host, ssh_port, ssh_user, oracle_host, oracle_port, oracle_sid, status, last_heartbeat)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'online', ?)
            ''', (
                agent_data['agent_id'],
                agent_data['host'],
                agent_data.get('ssh_port', 22),
                agent_data.get('ssh_user', 'oracle'),
                agent_data['oracle_host'],
                agent_data.get('oracle_port', 1521),
                agent_data['oracle_sid'],
                datetime.now()
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Agent已注册: {agent_data['agent_id']}")
            return True
            
        except Exception as e:
            self.logger.error(f"注册Agent失败: {e}")
            return False
    
    def execute_remote_task(self, agent_id: str, task_data: dict) -> Tuple[bool, str]:
        """通过SSH远程执行任务"""
        try:
            # 获取Agent信息
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT host, ssh_port, ssh_user FROM agents WHERE agent_id = ?
            ''', (agent_id,))
            
            agent_info = cursor.fetchone()
            conn.close()
            
            if not agent_info:
                return False, f"Agent不存在: {agent_id}"
            
            host, ssh_port, ssh_user = agent_info
            
            # 获取SSH连接
            ssh = self.ssh_manager.get_connection(
                host=host,
                port=ssh_port,
                username=ssh_user,
                key_file=os.path.expanduser(self.config['ssh']['key_file'])
            )
            
            # 构建命令
            task_json = json.dumps(task_data).replace('"', '\\"')
            command = f'cd /opt/oracle_archive_agent && ./archive_agent.sh execute "{task_json}"'
            
            # 执行命令
            stdin, stdout, stderr = ssh.exec_command(command, timeout=self.config['ssh']['timeout'])
            
            # 获取输出
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            exit_code = stdout.channel.recv_exit_status()
            
            if exit_code == 0:
                self.logger.info(f"任务执行成功: {task_data['task_id']}")
                return True, output
            else:
                self.logger.error(f"任务执行失败: {error}")
                return False, error
                
        except Exception as e:
            self.logger.error(f"远程执行任务失败: {e}")
            return False, str(e)
    
    def create_archive_tasks(self, agent_id: str = None) -> List[int]:
        """创建归档任务"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查询需要归档的表
            if agent_id:
                cursor.execute('''
                    SELECT id, agent_id FROM archive_tables 
                    WHERE agent_id = ? AND enabled = 1
                ''', (agent_id,))
            else:
                cursor.execute('''
                    SELECT id, agent_id FROM archive_tables 
                    WHERE enabled = 1
                ''')
            
            tables = cursor.fetchall()
            task_ids = []
            
            for table_id, agent_id in tables:
                cursor.execute('''
                    INSERT INTO archive_tasks (agent_id, table_id, status)
                    VALUES (?, ?, 'pending')
                ''', (agent_id, table_id))
                
                task_ids.append(cursor.lastrowid)
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"创建了 {len(task_ids)} 个归档任务")
            return task_ids
            
        except Exception as e:
            self.logger.error(f"创建任务失败: {e}")
            return []
    
    def dispatch_tasks(self):
        """分发任务到Agent"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取待处理的任务
            cursor.execute('''
                SELECT t.id, t.agent_id, t.table_id,
                       at.table_name, at.schema_name, at.date_field, at.retention_days, at.batch_size
                FROM archive_tasks t
                JOIN archive_tables at ON t.table_id = at.id
                WHERE t.status = 'pending'
            ''')
            
            tasks = cursor.fetchall()
            
            for task in tasks:
                task_id, agent_id, table_id, table_name, schema_name, date_field, retention_days, batch_size = task
                
                # 构建任务数据
                task_data = {
                    'task_id': task_id,
                    'table_name': table_name,
                    'schema_name': schema_name,
                    'date_field': date_field,
                    'retention_days': retention_days,
                    'batch_size': batch_size
                }
                
                # 更新任务状态为分发中
                cursor.execute('''
                    UPDATE archive_tasks 
                    SET status = 'dispatching', start_time = ?
                    WHERE id = ?
                ''', (datetime.now(), task_id))
                conn.commit()
                
                # 在后台线程执行任务
                thread = threading.Thread(
                    target=self._execute_task_thread,
                    args=(task_id, agent_id, task_data),
                    daemon=True
                )
                thread.start()
                
                self.logger.info(f"任务 {task_id} 已分发到Agent {agent_id}")
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"分发任务失败: {e}")
    
    def _execute_task_thread(self, task_id: int, agent_id: str, task_data: dict):
        """在后台线程执行任务"""
        try:
            # 执行远程任务
            success, result = self.execute_remote_task(agent_id, task_data)
            
            # 更新任务状态
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if success:
                cursor.execute('''
                    UPDATE archive_tasks 
                    SET status = 'completed', end_time = ?
                    WHERE id = ?
                ''', (datetime.now(), task_id))
            else:
                cursor.execute('''
                    UPDATE archive_tasks 
                    SET status = 'failed', end_time = ?, error_message = ?
                    WHERE id = ?
                ''', (datetime.now(), result, task_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"执行任务线程异常: {e}")
    
    def get_dashboard_stats(self) -> dict:
        """获取仪表板统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Agent统计
            cursor.execute('SELECT COUNT(*) FROM agents')
            total_agents = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM agents WHERE status = "online"')
            online_agents = cursor.fetchone()[0]
            
            # 表统计
            cursor.execute('SELECT COUNT(*) FROM archive_tables WHERE enabled = 1')
            enabled_tables = cursor.fetchone()[0]
            
            # 任务统计
            cursor.execute('SELECT COUNT(*) FROM archive_tasks WHERE DATE(created_at) = DATE("now")')
            today_tasks = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM archive_tasks WHERE status = "completed"')
            completed_tasks = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM archive_tasks WHERE status = "failed"')
            failed_tasks = cursor.fetchone()[0]
            
            # 存储统计
            storage_path = Path(self.config['storage']['local_path'])
            total_size = sum(f.stat().st_size for f in storage_path.rglob('*') if f.is_file())
            
            conn.close()
            
            return {
                'total_agents': total_agents,
                'online_agents': online_agents,
                'enabled_tables': enabled_tables,
                'today_tasks': today_tasks,
                'completed_tasks': completed_tasks,
                'failed_tasks': failed_tasks,
                'storage_used': total_size
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def cleanup(self):
        """清理资源"""
        self.ssh_manager.close_all()


class CentralWebApp:
    """中央控制Web应用"""
    
    def __init__(self, server: CentralControlServer):
        self.server = server
        self.app = Flask(__name__)
        self.app.secret_key = 'central_control_secret_key_2024'
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def dashboard():
            """仪表板"""
            stats = self.server.get_dashboard_stats()
            return render_template('dashboard.html', stats=stats)
        
        @self.app.route('/agents')
        def agents():
            """Agent管理"""
            conn = sqlite3.connect(self.server.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT agent_id, host, oracle_host, oracle_sid, status, last_heartbeat
                FROM agents
                ORDER BY agent_id
            ''')
            agents = cursor.fetchall()
            conn.close()
            
            return render_template('agents.html', agents=agents)
        
        @self.app.route('/tables')
        def tables():
            """表配置管理"""
            conn = sqlite3.connect(self.server.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT at.id, at.agent_id, at.schema_name, at.table_name, 
                       at.retention_days, at.enabled
                FROM archive_tables at
                ORDER BY at.agent_id, at.schema_name, at.table_name
            ''')
            tables = cursor.fetchall()
            
            # 获取调度信息
            cursor.execute('''
                SELECT table_id, cron_expression 
                FROM table_schedules 
                WHERE enabled = 1
            ''')
            schedules = {row[0]: row[1] for row in cursor.fetchall()}
            
            conn.close()
            
            return render_template('tables.html', tables=tables, schedules=schedules)
        
        @self.app.route('/tasks')
        def tasks():
            """任务管理"""
            conn = sqlite3.connect(self.server.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT t.id, t.agent_id, at.table_name, t.status, 
                       t.start_time, t.end_time, t.error_message
                FROM archive_tasks t
                JOIN archive_tables at ON t.table_id = at.id
                ORDER BY t.created_at DESC
                LIMIT 100
            ''')
            tasks = cursor.fetchall()
            conn.close()
            
            return render_template('tasks.html', tasks=tasks)
        
        @self.app.route('/api/agent/register', methods=['POST'])
        def register_agent():
            """注册Agent API"""
            try:
                data = request.get_json()
                success = self.server.register_agent(data)
                return jsonify({'success': success})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/agent/heartbeat', methods=['POST'])
        def agent_heartbeat():
            """Agent心跳API"""
            try:
                data = request.get_json()
                agent_id = data.get('agent_id')
                
                conn = sqlite3.connect(self.server.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE agents 
                    SET status = 'online', last_heartbeat = ?
                    WHERE agent_id = ?
                ''', (datetime.now(), agent_id))
                
                conn.commit()
                conn.close()
                
                return jsonify({'success': True})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/task/create', methods=['POST'])
        def create_tasks():
            """创建任务API"""
            try:
                agent_id = request.json.get('agent_id')
                task_ids = self.server.create_archive_tasks(agent_id)
                
                # 立即分发任务
                self.server.dispatch_tasks()
                
                return jsonify({'success': True, 'task_count': len(task_ids)})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/task/status', methods=['POST'])
        def update_task_status():
            """更新任务状态API"""
            try:
                data = request.get_json()
                task_id = data.get('task_id')
                status = data.get('status')
                details = data.get('details', {})
                
                conn = sqlite3.connect(self.server.db_path)
                cursor = conn.cursor()
                
                # 更新基本状态
                cursor.execute('''
                    UPDATE archive_tasks 
                    SET status = ?
                    WHERE id = ?
                ''', (status, task_id))
                
                # 更新详细信息
                if 'records_exported' in details:
                    cursor.execute('''
                        UPDATE archive_tasks 
                        SET records_exported = ?, records_deleted = ?, 
                            file_size = ?, file_path = ?
                        WHERE id = ?
                    ''', (
                        details.get('records_exported', 0),
                        details.get('records_deleted', 0),
                        details.get('file_size', 0),
                        details.get('file_name', ''),
                        task_id
                    ))
                
                conn.commit()
                conn.close()
                
                return jsonify({'success': True})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/upload', methods=['POST'])
        def upload_file():
            """文件上传API"""
            try:
                if 'file' not in request.files:
                    return jsonify({'success': False, 'error': '没有文件'})
                
                file = request.files['file']
                task_id = request.form.get('task_id')
                agent_id = request.form.get('agent_id')
                
                if file.filename == '':
                    return jsonify({'success': False, 'error': '文件名为空'})
                
                # 保存文件
                filename = secure_filename(file.filename)
                storage_path = Path(self.server.config['storage']['local_path'])
                agent_path = storage_path / agent_id
                agent_path.mkdir(exist_ok=True)
                
                file_path = agent_path / filename
                file.save(str(file_path))
                
                self.server.logger.info(f"文件上传成功: {file_path}")
                
                return jsonify({
                    'success': True,
                    'path': str(file_path.relative_to(storage_path))
                })
                
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/schedule/create', methods=['POST'])
        def create_schedule():
            """创建或更新定时计划"""
            try:
                data = request.get_json()
                table_id = data.get('table_id')
                cron_expression = data.get('cron_expression')
                enabled = data.get('enabled', True)
                
                conn = sqlite3.connect(self.server.db_path)
                cursor = conn.cursor()
                
                # 计算下次运行时间
                from croniter import croniter
                base = datetime.now()
                cron = croniter(cron_expression, base)
                next_run = cron.get_next(datetime)
                
                cursor.execute('''
                    INSERT OR REPLACE INTO table_schedules 
                    (table_id, cron_expression, enabled, next_run, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (table_id, cron_expression, enabled, next_run, datetime.now()))
                
                conn.commit()
                conn.close()
                
                return jsonify({'success': True, 'next_run': next_run.isoformat()})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/schedule/delete', methods=['POST'])
        def delete_schedule():
            """删除定时计划"""
            try:
                data = request.get_json()
                table_id = data.get('table_id')
                
                conn = sqlite3.connect(self.server.db_path)
                cursor = conn.cursor()
                
                cursor.execute('DELETE FROM table_schedules WHERE table_id = ?', (table_id,))
                
                conn.commit()
                conn.close()
                
                return jsonify({'success': True})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/schedule/get/<int:table_id>')
        def get_schedule(table_id):
            """获取定时计划"""
            try:
                conn = sqlite3.connect(self.server.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT cron_expression, enabled, last_run, next_run 
                    FROM table_schedules 
                    WHERE table_id = ?
                ''', (table_id,))
                
                row = cursor.fetchone()
                conn.close()
                
                if row:
                    return jsonify({
                        'success': True,
                        'schedule': {
                            'cron_expression': row[0],
                            'enabled': row[1],
                            'last_run': row[2],
                            'next_run': row[3]
                        }
                    })
                else:
                    return jsonify({'success': True, 'schedule': None})
                    
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/health')
        def health_check():
            """健康检查API"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat()
            })
    
    def run(self):
        """运行Web应用"""
        config = self.server.config['server']
        self.app.run(
            host=config['host'],
            port=config['port'],
            debug=config['debug']
        )


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Oracle 分布式归档系统 - 中央控制服务')
    parser.add_argument('--config', '-c', default='central_config.yaml', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 创建服务器
    server = CentralControlServer(args.config)
    
    # 创建Web应用
    web_app = CentralWebApp(server)
    
    # 启动调度器（后台线程）
    def scheduler_loop():
        while True:
            try:
                # 检查离线Agent
                conn = sqlite3.connect(server.db_path)
                cursor = conn.cursor()
                
                timeout = datetime.now() - timedelta(minutes=5)
                cursor.execute('''
                    UPDATE agents 
                    SET status = 'offline'
                    WHERE last_heartbeat < ? AND status = 'online'
                ''', (timeout,))
                
                # 检查定时任务
                cursor.execute('''
                    SELECT ts.id, ts.table_id, ts.cron_expression, ts.next_run,
                           at.agent_id
                    FROM table_schedules ts
                    JOIN archive_tables at ON ts.table_id = at.id
                    WHERE ts.enabled = 1 
                    AND ts.next_run <= ?
                    AND at.enabled = 1
                ''', (datetime.now(),))
                
                scheduled_tasks = cursor.fetchall()
                
                for schedule in scheduled_tasks:
                    schedule_id, table_id, cron_expr, next_run, agent_id = schedule
                    
                    # 创建任务
                    cursor.execute('''
                        INSERT INTO archive_tasks (agent_id, table_id, status)
                        VALUES (?, ?, 'pending')
                    ''', (agent_id, table_id))
                    
                    task_id = cursor.lastrowid
                    server.logger.info(f"定时任务创建: 表ID={table_id}, 任务ID={task_id}")
                    
                    # 更新下次运行时间
                    try:
                        from croniter import croniter
                        cron = croniter(cron_expr, datetime.now())
                        new_next_run = cron.get_next(datetime)
                        
                        cursor.execute('''
                            UPDATE table_schedules 
                            SET last_run = ?, next_run = ?
                            WHERE id = ?
                        ''', (datetime.now(), new_next_run, schedule_id))
                    except Exception as e:
                        server.logger.error(f"更新调度时间失败: {e}")
                
                conn.commit()
                conn.close()
                
                # 分发待处理任务
                server.dispatch_tasks()
                
            except Exception as e:
                server.logger.error(f"调度器异常: {e}")
            
            time.sleep(server.config['scheduler']['check_interval'])
    
    if server.config['scheduler']['enabled']:
        scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
        scheduler_thread.start()
    
    try:
        print("中央控制服务已启动")
        print(f"Web管理界面: http://localhost:{server.config['server']['port']}")
        print("按 Ctrl+C 停止服务")
        
        web_app.run()
        
    except KeyboardInterrupt:
        print("\n服务停止中...")
        server.cleanup()
        print("服务已停止")


if __name__ == "__main__":
    main() 