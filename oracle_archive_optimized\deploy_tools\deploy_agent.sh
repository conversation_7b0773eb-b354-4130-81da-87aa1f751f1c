#!/bin/bash
#############################################################################
# Oracle Archive Agent 部署脚本
# 用于在Oracle数据库服务器上部署Shell Agent
#############################################################################

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AGENT_DIR="/opt/oracle_archive_agent"
AGENT_USER="oracle"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查是否以root运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    echo_info "检查系统依赖..."
    
    local deps=("curl" "jq" "gzip" "tar" "zip")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing+=("$dep")
        fi
    done
    
    if [ ${#missing[@]} -gt 0 ]; then
        echo_error "缺少以下依赖: ${missing[*]}"
        echo_info "请先安装: yum install -y ${missing[*]} 或 apt-get install -y ${missing[*]}"
        exit 1
    fi
    
    echo_info "依赖检查通过"
}

# 创建目录结构
create_directories() {
    echo_info "创建目录结构..."
    
    mkdir -p "$AGENT_DIR"/{logs,work,temp}
    
    # 设置权限
    chown -R "$AGENT_USER":"$AGENT_USER" "$AGENT_DIR"
    chmod 755 "$AGENT_DIR"
    
    echo_info "目录创建完成"
}

# 复制文件
copy_files() {
    echo_info "复制Agent文件..."
    
    # 复制主脚本
    cp "${SCRIPT_DIR}/../shell_agents/archive_agent.sh" "$AGENT_DIR/"
    chmod +x "$AGENT_DIR/archive_agent.sh"
    
    # 复制配置模板
    cp "${SCRIPT_DIR}/../shell_agents/agent.conf.template" "$AGENT_DIR/agent.conf"
    
    # 设置权限
    chown -R "$AGENT_USER":"$AGENT_USER" "$AGENT_DIR"
    
    echo_info "文件复制完成"
}

# 配置Agent
configure_agent() {
    echo_info "配置Agent..."
    
    local config_file="$AGENT_DIR/agent.conf"
    
    # 获取输入
    read -p "请输入Agent ID [oracle_agent_01]: " agent_id
    agent_id=${agent_id:-oracle_agent_01}
    
    read -p "请输入Oracle Home [/u01/app/oracle/product/19.0.0/dbhome_1]: " oracle_home
    oracle_home=${oracle_home:-/u01/app/oracle/product/19.0.0/dbhome_1}
    
    read -p "请输入Oracle SID [ORCL]: " oracle_sid
    oracle_sid=${oracle_sid:-ORCL}
    
    read -p "请输入归档用户名 [archive_user]: " db_user
    db_user=${db_user:-archive_user}
    
    read -s -p "请输入归档用户密码: " db_pass
    echo
    
    read -p "请输入中央服务器地址 [http://localhost:8080]: " central_server
    central_server=${central_server:-http://localhost:8080}
    
    read -p "请输入备份服务器地址: " upload_server
    read -p "请输入备份路径 [/backup/oracle_archives]: " upload_path
    upload_path=${upload_path:-/backup/oracle_archives}
    
    # 更新配置文件
    sed -i "s|AGENT_ID=.*|AGENT_ID=\"$agent_id\"|" "$config_file"
    sed -i "s|ORACLE_HOME=.*|ORACLE_HOME=\"$oracle_home\"|" "$config_file"
    sed -i "s|ORACLE_SID=.*|ORACLE_SID=\"$oracle_sid\"|" "$config_file"
    sed -i "s|DB_USER=.*|DB_USER=\"$db_user\"|" "$config_file"
    sed -i "s|DB_PASS=.*|DB_PASS=\"$db_pass\"|" "$config_file"
    sed -i "s|CENTRAL_SERVER=.*|CENTRAL_SERVER=\"$central_server\"|" "$config_file"
    sed -i "s|UPLOAD_SERVER=.*|UPLOAD_SERVER=\"$upload_server\"|" "$config_file"
    sed -i "s|UPLOAD_PATH=.*|UPLOAD_PATH=\"$upload_path\"|" "$config_file"
    
    echo_info "配置完成"
}

# 创建systemd服务
create_service() {
    echo_info "创建系统服务..."
    
    cat > /etc/systemd/system/oracle-archive-agent.service <<EOF
[Unit]
Description=Oracle Archive Agent
After=network.target

[Service]
Type=simple
User=$AGENT_USER
WorkingDirectory=$AGENT_DIR
ExecStart=$AGENT_DIR/archive_agent.sh start
ExecStop=$AGENT_DIR/archive_agent.sh stop
Restart=on-failure
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载systemd
    systemctl daemon-reload
    
    echo_info "服务创建完成"
}

# 配置SSH密钥（用于SCP上传）
setup_ssh_keys() {
    echo_info "配置SSH密钥..."
    
    local ssh_dir="/home/<USER>/.ssh"
    
    if [ ! -d "$ssh_dir" ]; then
        mkdir -p "$ssh_dir"
        chown "$AGENT_USER":"$AGENT_USER" "$ssh_dir"
        chmod 700 "$ssh_dir"
    fi
    
    # 生成SSH密钥
    if [ ! -f "$ssh_dir/id_rsa" ]; then
        echo_info "生成SSH密钥..."
        su - "$AGENT_USER" -c "ssh-keygen -t rsa -b 2048 -f ~/.ssh/id_rsa -N ''"
    fi
    
    echo_warn "请将以下公钥添加到备份服务器的 authorized_keys:"
    cat "$ssh_dir/id_rsa.pub"
    echo
    
    read -p "公钥已添加到备份服务器？[y/N] " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo_warn "请在添加公钥后手动启动服务"
    fi
}

# 测试Agent
test_agent() {
    echo_info "测试Agent配置..."
    
    su - "$AGENT_USER" -c "cd $AGENT_DIR && ./archive_agent.sh test"
    
    if [ $? -eq 0 ]; then
        echo_info "测试通过"
        return 0
    else
        echo_error "测试失败，请检查配置"
        return 1
    fi
}

# 启动服务
start_service() {
    echo_info "启动Agent服务..."
    
    systemctl enable oracle-archive-agent
    systemctl start oracle-archive-agent
    
    sleep 2
    
    if systemctl is-active --quiet oracle-archive-agent; then
        echo_info "服务启动成功"
        systemctl status oracle-archive-agent
    else
        echo_error "服务启动失败"
        journalctl -u oracle-archive-agent -n 20
        exit 1
    fi
}

# 主函数
main() {
    echo "Oracle Archive Agent 部署脚本"
    echo "============================="
    echo
    
    check_root
    check_dependencies
    create_directories
    copy_files
    configure_agent
    create_service
    setup_ssh_keys
    
    if test_agent; then
        start_service
        
        echo
        echo_info "部署完成！"
        echo_info "Agent已安装到: $AGENT_DIR"
        echo_info "服务管理命令:"
        echo_info "  启动: systemctl start oracle-archive-agent"
        echo_info "  停止: systemctl stop oracle-archive-agent"
        echo_info "  状态: systemctl status oracle-archive-agent"
        echo_info "  日志: journalctl -u oracle-archive-agent -f"
    else
        echo_error "部署失败，请检查配置后重试"
        exit 1
    fi
}

# 卸载函数
uninstall() {
    echo_warn "即将卸载Oracle Archive Agent"
    read -p "确定要继续吗？[y/N] " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo_info "停止服务..."
        systemctl stop oracle-archive-agent 2>/dev/null
        systemctl disable oracle-archive-agent 2>/dev/null
        
        echo_info "删除服务文件..."
        rm -f /etc/systemd/system/oracle-archive-agent.service
        systemctl daemon-reload
        
        echo_info "删除Agent文件..."
        rm -rf "$AGENT_DIR"
        
        echo_info "卸载完成"
    fi
}

# 处理命令行参数
case "${1:-install}" in
    install)
        main
        ;;
    uninstall)
        check_root
        uninstall
        ;;
    *)
        echo "用法: $0 {install|uninstall}"
        exit 1
        ;;
esac 