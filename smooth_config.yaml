# OpenList STRM下载工具配置文件 - 优化版本（减少卡顿）

# OpenList服务器配置
openlist:
  # OpenList服务器地址
  base_url: "http://192.168.1.7:5244"

  # 认证方式2：直接使用token（推荐）
  token: "alist-ad95f927-0cc9-4b8c-9a24-87f62ac29d5d0hKfv46OTHvRKQyxUtPcP6y33xlLRNPpFsI2VVMkuplCHum5RtkRGev7kst82Cc5"

  # 目录映射（每个源目录对应不同的本地目录）
  source_paths:
    "/123/EMBY/anime": "./downloads/anime"
    "/123/EMBY/Hentai": "./downloads/Hentai"

  # 目录密码（如果目录有密码保护）
  password: ""

# 本地存储配置
local:
  # 本地基础路径（STRM文件将保存到这里）
  base_path: "./downloads"

# STRM文件配置
strm:
  # URL格式设置
  url_format: "relative"

  # 自定义URL前缀（当url_format为"custom"时使用）
  custom_prefix: ""

  # 是否对URL进行编码
  url_encode: false

# 下载配置 - 优化设置
max_depth: 10
concurrent_downloads: 10 # 降低并发数以减少网络拥塞
download_metadata: true # 启用元数据下载（包括NFO、图片、字幕文件）
skip_error_dirs: true # 跳过有问题的目录（如超时目录）
request_delay: 0.5 # 请求间延迟（秒），避免触发服务器限流

# 运行模式配置
sync:
  mode: "incremental"
  cleanup_invalid: true
  confirm_mode: false # 禁用确认模式

# 高级配置 - 优化设置
advanced:
  timeout: 30 # 一般请求超时时间
  dir_timeout: 120 # 目录列表超时时间（针对大目录）
  retry_count: 2 # 减少重试次数
  retry_delay: 3 # 缩短重试延迟
  verify_ssl: false

  # 性能监控设置
  show_progress: true # 显示详细进度
  progress_interval: 500 # 每5个文件显示一次进度
  show_network_stats: true # 显示网络统计信息

# 过滤器配置
filter:
  video_extensions: []
  exclude_patterns: []
  min_file_size: 0
  max_file_size: 0

# 日志配置
logging:
  level: "INFO" # INFO级别显示重要进度，DEBUG显示详细信息
  show_timing: true # 显示操作耗时
  show_memory_usage: false # 不显示内存使用（减少日志量）
