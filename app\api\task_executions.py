from flask import request, jsonify, current_app
from app import db
from app.models import TaskExecution, ArchiveTask, SystemLog
from app.api import api_bp
from datetime import datetime, timedelta

@api_bp.route('/task-executions', methods=['GET'])
def get_task_executions():
    """获取任务执行记录列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        task_id = request.args.get('task_id', type=int)
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 构建查询
        query = TaskExecution.query
        
        if task_id:
            query = query.filter(TaskExecution.task_id == task_id)
        
        if status:
            query = query.filter(TaskExecution.status == status)
        
        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date)
                query = query.filter(TaskExecution.start_time >= start_dt)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date)
                query = query.filter(TaskExecution.start_time <= end_dt)
            except ValueError:
                pass
        
        # 按开始时间倒序排列
        query = query.order_by(TaskExecution.start_time.desc())
        
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return jsonify({
            'success': True,
            'data': {
                'items': [execution.to_dict() for execution in pagination.items],
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            }
        })
        
    except Exception as e:
        SystemLog.log_error('task_executions', f'获取任务执行记录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取任务执行记录失败: {str(e)}'
        }), 500

@api_bp.route('/task-executions/<int:execution_id>', methods=['GET'])
def get_task_execution(execution_id):
    """获取单个任务执行记录详情"""
    try:
        execution = TaskExecution.query.get_or_404(execution_id)
        
        return jsonify({
            'success': True,
            'data': execution.to_dict()
        })
        
    except Exception as e:
        SystemLog.log_error('task_executions', f'获取任务执行记录详情失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取任务执行记录详情失败: {str(e)}'
        }), 500

@api_bp.route('/task-executions/<int:execution_id>/log', methods=['GET'])
def get_execution_log(execution_id):
    """获取任务执行日志"""
    try:
        execution = TaskExecution.query.get_or_404(execution_id)
        
        return jsonify({
            'success': True,
            'data': {
                'execution_id': execution_id,
                'log': execution.execution_log or '暂无日志'
            }
        })
        
    except Exception as e:
        SystemLog.log_error('task_executions', f'获取任务执行日志失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取任务执行日志失败: {str(e)}'
        }), 500

@api_bp.route('/task-executions/<int:execution_id>/cancel', methods=['POST'])
def cancel_task_execution(execution_id):
    """取消正在执行的任务"""
    try:
        execution = TaskExecution.query.get_or_404(execution_id)
        
        if execution.status != 'running':
            return jsonify({
                'success': False,
                'message': '只能取消正在运行的任务'
            }), 400
        
        # 更新状态为已取消
        execution.status = 'cancelled'
        execution.end_time = datetime.utcnow()
        execution.add_log('任务被用户取消')
        
        db.session.commit()
        
        SystemLog.log_info('task_executions', f'取消任务执行: {execution_id}')
        
        return jsonify({
            'success': True,
            'message': '任务已取消',
            'data': execution.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('task_executions', f'取消任务执行失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'取消任务执行失败: {str(e)}'
        }), 500

@api_bp.route('/task-executions/statistics', methods=['GET'])
def get_execution_statistics():
    """获取执行统计信息"""
    try:
        # 获取时间范围参数
        days = request.args.get('days', 7, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 总执行次数
        total_executions = TaskExecution.query.filter(
            TaskExecution.start_time >= start_date
        ).count()
        
        # 成功执行次数
        success_executions = TaskExecution.query.filter(
            TaskExecution.start_time >= start_date,
            TaskExecution.status == 'success'
        ).count()
        
        # 失败执行次数
        failed_executions = TaskExecution.query.filter(
            TaskExecution.start_time >= start_date,
            TaskExecution.status == 'failed'
        ).count()
        
        # 正在运行的任务
        running_executions = TaskExecution.query.filter(
            TaskExecution.status == 'running'
        ).count()
        
        # 计算成功率
        success_rate = 0
        if total_executions > 0:
            success_rate = round((success_executions / total_executions) * 100, 2)
        
        # 获取最近的执行记录
        recent_executions = TaskExecution.query.order_by(
            TaskExecution.start_time.desc()
        ).limit(10).all()
        
        return jsonify({
            'success': True,
            'data': {
                'period_days': days,
                'total_executions': total_executions,
                'success_executions': success_executions,
                'failed_executions': failed_executions,
                'running_executions': running_executions,
                'success_rate': success_rate,
                'recent_executions': [exec.to_dict() for exec in recent_executions]
            }
        })
        
    except Exception as e:
        SystemLog.log_error('task_executions', f'获取执行统计信息失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取执行统计信息失败: {str(e)}'
        }), 500

@api_bp.route('/task-executions/cleanup', methods=['POST'])
def cleanup_old_executions():
    """清理旧的执行记录"""
    try:
        data = request.get_json()
        days = data.get('days', 30)  # 默认清理30天前的记录
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # 只清理已完成的记录（成功或失败）
        deleted_count = TaskExecution.query.filter(
            TaskExecution.start_time < cutoff_date,
            TaskExecution.status.in_(['success', 'failed', 'cancelled'])
        ).delete()
        
        db.session.commit()
        
        SystemLog.log_info('task_executions', f'清理了 {deleted_count} 条旧执行记录')
        
        return jsonify({
            'success': True,
            'message': f'成功清理了 {deleted_count} 条旧执行记录'
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('task_executions', f'清理旧执行记录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'清理旧执行记录失败: {str(e)}'
        }), 500
