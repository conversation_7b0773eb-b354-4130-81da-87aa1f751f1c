#!/usr/bin/env python3
"""
调试下载工具 - 用于测试单个文件的下载并输出详细信息
"""

import asyncio
import aiohttp
import yaml
import sys
import logging
from pathlib import Path

# 配置详细的调试日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


class DebugClient:
    """调试客户端"""
    
    def __init__(self, config):
        self.config = config
        self.base_url = config['openlist']['base_url'].rstrip('/')
        self.username = config['openlist'].get('username', '')
        self.password = config['openlist'].get('password', '')
        self.token = config['openlist'].get('token', '')
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        await self.login()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    async def login(self):
        """登录获取token"""
        if self.token:
            logger.info(f"使用配置的token: {self.token[:10]}...")
            return
            
        if not self.username:
            logger.info("未配置用户名和token，使用匿名访问")
            return
            
        login_url = f"{self.base_url}/api/auth/login"
        login_data = {"username": self.username, "password": self.password}
        
        logger.info(f"尝试登录: {login_url}")
        async with self.session.post(login_url, json=login_data) as resp:
            logger.info(f"登录响应状态: {resp.status}")
            if resp.status == 200:
                data = await resp.json()
                logger.info(f"登录响应: {data}")
                self.token = data.get('data', {}).get('token', '')
                if self.token:
                    logger.info(f"登录成功，token: {self.token[:10]}...")
                else:
                    logger.error("登录响应中未找到token")
            else:
                text = await resp.text()
                logger.error(f"登录失败: {resp.status}, {text}")
                
    def _get_headers(self):
        """获取请求头"""
        headers = {}
        if self.token:
            headers['Authorization'] = self.token
        return headers
        
    async def get_file_info(self, path: str, password: str = ""):
        """获取文件信息"""
        get_url = f"{self.base_url}/api/fs/get"
        data = {"path": path, "password": password}
        
        logger.info(f"获取文件信息: {get_url}")
        logger.info(f"请求数据: {data}")
        logger.info(f"请求头: {self._get_headers()}")
        
        async with self.session.post(get_url, json=data, headers=self._get_headers()) as resp:
            logger.info(f"文件信息响应状态: {resp.status}")
            logger.info(f"响应头: {dict(resp.headers)}")
            
            if resp.status == 200:
                result = await resp.json()
                logger.info(f"文件信息响应: {result}")
                return result.get('data', {})
            else:
                text = await resp.text()
                logger.error(f"获取文件信息失败: {resp.status}, {text}")
                return {}
                
    async def debug_download(self, remote_path: str, password: str = ""):
        """调试下载单个文件"""
        logger.info("=" * 80)
        logger.info(f"开始调试下载: {remote_path}")
        logger.info("=" * 80)
        
        # 1. 获取文件信息
        file_info = await self.get_file_info(remote_path, password)
        if not file_info:
            logger.error("无法获取文件信息，终止下载")
            return
            
        # 2. 提取下载URL
        raw_url = file_info.get('raw_url', '')
        if not raw_url:
            logger.error("文件信息中没有raw_url字段")
            logger.info(f"可用字段: {list(file_info.keys())}")
            return
            
        logger.info(f"下载URL: {raw_url}")
        
        # 3. 测试下载
        logger.info("开始测试下载...")
        async with self.session.get(raw_url, headers=self._get_headers()) as resp:
            logger.info(f"下载响应状态: {resp.status}")
            logger.info(f"下载响应头: {dict(resp.headers)}")
            
            if resp.status == 200:
                content = await resp.read()
                file_size = len(content)
                logger.info(f"实际下载大小: {file_size} bytes")
                
                # 分析内容
                if file_size < 100:
                    logger.warning("文件太小，可能是错误页面")
                    logger.warning(f"内容(文本): {content}")
                    logger.warning(f"内容(十六进制): {content.hex()}")
                elif content.startswith(b'<!DOCTYPE') or content.startswith(b'<html'):
                    logger.warning("下载到的是HTML页面")
                    logger.warning(f"HTML内容: {content[:500]}")
                else:
                    logger.info("文件内容看起来正常")
                    logger.info(f"文件开头(十六进制): {content[:20].hex()}")
                    
                    # 保存到临时文件
                    temp_file = Path(f"debug_{Path(remote_path).name}")
                    with open(temp_file, 'wb') as f:
                        f.write(content)
                    logger.info(f"文件已保存到: {temp_file}")
                    
            else:
                error_content = await resp.text()
                logger.error(f"下载失败: {resp.status}")
                logger.error(f"错误内容: {error_content}")


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python debug_download.py <config_file> <remote_path> [password]")
        print("示例: python debug_download.py config.yaml '/123/EMBY/test.jpg'")
        return
        
    config_file = sys.argv[1]
    remote_path = sys.argv[2]
    password = sys.argv[3] if len(sys.argv) > 3 else ""
    
    # 加载配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return
        
    # 开始调试
    async with DebugClient(config) as client:
        await client.debug_download(remote_path, password)


if __name__ == '__main__':
    asyncio.run(main()) 