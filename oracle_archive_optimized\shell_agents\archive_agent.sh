#!/bin/bash
#############################################################################
# Oracle Archive Agent - Shell Script Version
# 在Oracle数据库服务器上运行，执行归档任务
#############################################################################

# 默认配置
AGENT_HOME="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${AGENT_HOME}/agent.conf"
LOG_DIR="${AGENT_HOME}/logs"
WORK_DIR="${AGENT_HOME}/work"
TEMP_DIR="${AGENT_HOME}/temp"

# 日志级别
LOG_LEVEL="INFO"

# 创建必要的目录
mkdir -p "$LOG_DIR" "$WORK_DIR" "$TEMP_DIR"

# 日志文件
LOG_FILE="${LOG_DIR}/archive_agent_$(date +%Y%m%d).log"

#############################################################################
# 日志函数
#############################################################################
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
}

log_error() {
    log "ERROR" "$@"
}

log_debug() {
    if [ "$LOG_LEVEL" = "DEBUG" ]; then
        log "DEBUG" "$@"
    fi
}

#############################################################################
# 配置加载函数
#############################################################################
load_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    # 加载配置文件
    source "$CONFIG_FILE"
    
    # 验证必要的配置项
    local required_vars=(
        "ORACLE_HOME"
        "ORACLE_SID"
        "DB_USER"
        "DB_PASS"
        "CENTRAL_SERVER"
        "AGENT_ID"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "缺少配置项: $var"
            return 1
        fi
    done
    
    return 0
}

#############################################################################
# Oracle数据导出函数
#############################################################################
export_oracle_data() {
    local table_name=$1
    local schema_name=$2
    local date_field=$3
    local retention_days=$4
    local output_file=$5
    
    log_info "开始导出表: ${schema_name}.${table_name}"
    
    # 设置Oracle环境变量
    export ORACLE_HOME
    export ORACLE_SID
    export PATH=$ORACLE_HOME/bin:$PATH
    export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH
    
    # 计算截止日期
    local cutoff_date=$(date -d "${retention_days} days ago" +%Y-%m-%d)
    
    # 创建导出SQL
    local sql_file="${TEMP_DIR}/export_${table_name}.sql"
    cat > "$sql_file" <<EOF
SET PAGESIZE 0
SET LINESIZE 32767
SET FEEDBACK OFF
SET VERIFY OFF
SET HEADING OFF
SET TERMOUT OFF
SET TRIMSPOOL ON
SET TRIMOUT ON
SET COLSEP ','

SPOOL ${output_file}

SELECT * FROM ${schema_name}.${table_name}
WHERE ${date_field} < TO_DATE('${cutoff_date}', 'YYYY-MM-DD')
ORDER BY ${date_field};

SPOOL OFF
EXIT;
EOF
    
    # 执行导出
    log_debug "执行SQL导出..."
    sqlplus -S "${DB_USER}/${DB_PASS}" @"$sql_file" > /dev/null 2>&1
    local exit_code=$?
    
    # 清理临时文件
    rm -f "$sql_file"
    
    if [ $exit_code -eq 0 ]; then
        local row_count=$(wc -l < "$output_file" 2>/dev/null || echo "0")
        log_info "导出完成，共导出 $row_count 条记录"
        return 0
    else
        log_error "导出失败，退出码: $exit_code"
        return 1
    fi
}

#############################################################################
# 文件压缩函数
#############################################################################
compress_file() {
    local input_file=$1
    local compression_type=${COMPRESSION_TYPE:-"gzip"}
    local compression_level=${COMPRESSION_LEVEL:-6}
    
    if [ ! -f "$input_file" ]; then
        log_error "输入文件不存在: $input_file"
        return 1
    fi
    
    local output_file=""
    
    case "$compression_type" in
        "gzip")
            output_file="${input_file}.gz"
            log_info "使用gzip压缩文件..."
            gzip -${compression_level} -c "$input_file" > "$output_file"
            ;;
        "zip")
            output_file="${input_file}.zip"
            log_info "使用zip压缩文件..."
            zip -${compression_level} -j "$output_file" "$input_file" > /dev/null 2>&1
            ;;
        "tar.gz")
            output_file="${input_file}.tar.gz"
            log_info "使用tar.gz压缩文件..."
            tar czf "$output_file" -C "$(dirname "$input_file")" "$(basename "$input_file")"
            ;;
        *)
            log_error "不支持的压缩类型: $compression_type"
            return 1
            ;;
    esac
    
    if [ -f "$output_file" ]; then
        local original_size=$(stat -c%s "$input_file" 2>/dev/null || echo "0")
        local compressed_size=$(stat -c%s "$output_file" 2>/dev/null || echo "0")
        local ratio=0
        if [ $original_size -gt 0 ]; then
            ratio=$((100 - (compressed_size * 100 / original_size)))
        fi
        log_info "压缩完成，压缩率: ${ratio}%"
        echo "$output_file"
        return 0
    else
        log_error "压缩失败"
        return 1
    fi
}

#############################################################################
# 文件上传函数
#############################################################################
upload_file() {
    local file_path=$1
    local task_id=$2
    local upload_type=${UPLOAD_TYPE:-"scp"}
    
    if [ ! -f "$file_path" ]; then
        log_error "上传文件不存在: $file_path"
        return 1
    fi
    
    local remote_path=""
    
    case "$upload_type" in
        "scp")
            remote_path="${UPLOAD_SERVER}:${UPLOAD_PATH}/$(basename "$file_path")"
            log_info "使用SCP上传文件到: $remote_path"
            scp -o StrictHostKeyChecking=no "$file_path" "$remote_path"
            ;;
        "sftp")
            log_info "使用SFTP上传文件..."
            sftp -o StrictHostKeyChecking=no "${UPLOAD_SERVER}" <<EOF
cd ${UPLOAD_PATH}
put ${file_path}
bye
EOF
            ;;
        "http")
            log_info "使用HTTP上传文件..."
            curl -X POST \
                -F "file=@${file_path}" \
                -F "task_id=${task_id}" \
                -F "agent_id=${AGENT_ID}" \
                "${CENTRAL_SERVER}/api/upload" \
                -s -o /dev/null
            ;;
        *)
            log_error "不支持的上传类型: $upload_type"
            return 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        log_info "文件上传成功"
        return 0
    else
        log_error "文件上传失败"
        return 1
    fi
}

#############################################################################
# 删除已归档数据函数
#############################################################################
delete_archived_data() {
    local table_name=$1
    local schema_name=$2
    local date_field=$3
    local retention_days=$4
    local batch_size=${5:-10000}
    
    if [ "${DELETE_AFTER_ARCHIVE}" != "true" ]; then
        log_info "跳过数据删除（配置为不删除）"
        return 0
    fi
    
    log_info "开始删除已归档数据: ${schema_name}.${table_name}"
    
    # 计算截止日期
    local cutoff_date=$(date -d "${retention_days} days ago" +%Y-%m-%d)
    
    # 创建删除SQL
    local sql_file="${TEMP_DIR}/delete_${table_name}.sql"
    cat > "$sql_file" <<EOF
SET SERVEROUTPUT ON
DECLARE
    v_deleted NUMBER := 0;
    v_total_deleted NUMBER := 0;
BEGIN
    LOOP
        DELETE FROM ${schema_name}.${table_name}
        WHERE ${date_field} < TO_DATE('${cutoff_date}', 'YYYY-MM-DD')
        AND ROWNUM <= ${batch_size};
        
        v_deleted := SQL%ROWCOUNT;
        v_total_deleted := v_total_deleted + v_deleted;
        
        COMMIT;
        
        EXIT WHEN v_deleted = 0;
        
        DBMS_OUTPUT.PUT_LINE('Deleted ' || v_deleted || ' rows, total: ' || v_total_deleted);
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('Total deleted: ' || v_total_deleted || ' rows');
END;
/
EXIT;
EOF
    
    # 执行删除
    log_debug "执行数据删除..."
    sqlplus -S "${DB_USER}/${DB_PASS}" @"$sql_file" | tee -a "$LOG_FILE"
    local exit_code=$?
    
    # 清理临时文件
    rm -f "$sql_file"
    
    if [ $exit_code -eq 0 ]; then
        log_info "数据删除完成"
        return 0
    else
        log_error "数据删除失败，退出码: $exit_code"
        return 1
    fi
}

#############################################################################
# 向中央服务器报告状态
#############################################################################
report_status() {
    local task_id=$1
    local status=$2
    local message=$3
    local details=$4
    
    log_debug "向中央服务器报告状态: task_id=$task_id, status=$status"
    
    local json_data="{
        \"task_id\": ${task_id},
        \"agent_id\": \"${AGENT_ID}\",
        \"status\": \"${status}\",
        \"message\": \"${message}\",
        \"details\": ${details:-{}},
        \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
    }"
    
    curl -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        "${CENTRAL_SERVER}/api/task/status" \
        -s -o /dev/null
    
    if [ $? -eq 0 ]; then
        log_debug "状态报告成功"
        return 0
    else
        log_error "状态报告失败"
        return 1
    fi
}

#############################################################################
# 执行归档任务
#############################################################################
execute_archive_task() {
    local task_json=$1
    
    # 解析任务参数
    local task_id=$(echo "$task_json" | jq -r '.task_id')
    local table_name=$(echo "$task_json" | jq -r '.table_name')
    local schema_name=$(echo "$task_json" | jq -r '.schema_name')
    local date_field=$(echo "$task_json" | jq -r '.date_field')
    local retention_days=$(echo "$task_json" | jq -r '.retention_days')
    local batch_size=$(echo "$task_json" | jq -r '.batch_size // 10000')
    
    log_info "开始执行归档任务 #${task_id}: ${schema_name}.${table_name}"
    
    # 报告任务开始
    report_status "$task_id" "running" "任务开始执行" "{\"start_time\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"}"
    
    # 生成文件名
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local export_file="${WORK_DIR}/${schema_name}_${table_name}_${timestamp}.csv"
    
    # 1. 导出数据
    if ! export_oracle_data "$table_name" "$schema_name" "$date_field" "$retention_days" "$export_file"; then
        report_status "$task_id" "failed" "数据导出失败" "{\"error\": \"Export failed\"}"
        return 1
    fi
    
    # 检查是否有数据
    if [ ! -s "$export_file" ]; then
        log_info "没有需要归档的数据"
        report_status "$task_id" "completed" "没有需要归档的数据" "{\"records\": 0}"
        rm -f "$export_file"
        return 0
    fi
    
    # 2. 压缩文件
    local compressed_file=$(compress_file "$export_file")
    if [ -z "$compressed_file" ]; then
        report_status "$task_id" "failed" "文件压缩失败" "{\"error\": \"Compression failed\"}"
        rm -f "$export_file"
        return 1
    fi
    
    # 3. 上传文件
    if ! upload_file "$compressed_file" "$task_id"; then
        report_status "$task_id" "failed" "文件上传失败" "{\"error\": \"Upload failed\"}"
        rm -f "$export_file" "$compressed_file"
        return 1
    fi
    
    # 4. 删除已归档数据
    if ! delete_archived_data "$table_name" "$schema_name" "$date_field" "$retention_days" "$batch_size"; then
        log_error "删除数据失败，但归档已完成"
    fi
    
    # 5. 清理本地文件
    rm -f "$export_file" "$compressed_file"
    
    # 报告任务完成
    local file_size=$(stat -c%s "$compressed_file" 2>/dev/null || echo "0")
    local record_count=$(wc -l < "$export_file" 2>/dev/null || echo "0")
    
    report_status "$task_id" "completed" "任务完成" "{
        \"end_time\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
        \"records_exported\": ${record_count},
        \"file_size\": ${file_size},
        \"file_name\": \"$(basename "$compressed_file")\"
    }"
    
    log_info "归档任务 #${task_id} 执行完成"
    return 0
}

#############################################################################
# 心跳函数
#############################################################################
send_heartbeat() {
    while true; do
        curl -X POST \
            -H "Content-Type: application/json" \
            -d "{\"agent_id\": \"${AGENT_ID}\", \"status\": \"online\"}" \
            "${CENTRAL_SERVER}/api/agent/heartbeat" \
            -s -o /dev/null
        
        sleep ${HEARTBEAT_INTERVAL:-60}
    done
}

#############################################################################
# 主函数
#############################################################################
main() {
    local action=${1:-"help"}
    
    case "$action" in
        "start")
            log_info "Oracle Archive Agent 启动"
            
            # 加载配置
            if ! load_config; then
                log_error "加载配置失败"
                exit 1
            fi
            
            # 启动心跳（后台进程）
            send_heartbeat &
            HEARTBEAT_PID=$!
            
            log_info "Agent 已启动，等待任务..."
            log_info "心跳进程PID: $HEARTBEAT_PID"
            
            # 保存PID
            echo $$ > "${AGENT_HOME}/agent.pid"
            echo $HEARTBEAT_PID > "${AGENT_HOME}/heartbeat.pid"
            
            # 等待信号
            trap "log_info 'Agent 停止'; kill $HEARTBEAT_PID 2>/dev/null; exit 0" SIGTERM SIGINT
            
            while true; do
                sleep 10
            done
            ;;
            
        "execute")
            # 执行单个任务（由中央服务器通过SSH调用）
            local task_json=$2
            if [ -z "$task_json" ]; then
                log_error "缺少任务参数"
                exit 1
            fi
            
            # 加载配置
            if ! load_config; then
                log_error "加载配置失败"
                exit 1
            fi
            
            # 执行任务
            execute_archive_task "$task_json"
            ;;
            
        "test")
            # 测试模式
            log_info "测试模式"
            
            # 加载配置
            if ! load_config; then
                log_error "加载配置失败"
                exit 1
            fi
            
            # 测试数据库连接
            log_info "测试Oracle连接..."
            echo "SELECT 'OK' FROM DUAL;" | sqlplus -S "${DB_USER}/${DB_PASS}" | grep -q "OK"
            if [ $? -eq 0 ]; then
                log_info "数据库连接成功"
            else
                log_error "数据库连接失败"
                exit 1
            fi
            
            # 测试中央服务器连接
            log_info "测试中央服务器连接..."
            curl -s "${CENTRAL_SERVER}/api/health" > /dev/null
            if [ $? -eq 0 ]; then
                log_info "中央服务器连接成功"
            else
                log_error "中央服务器连接失败"
                exit 1
            fi
            
            log_info "所有测试通过"
            ;;
            
        "stop")
            # 停止Agent
            if [ -f "${AGENT_HOME}/agent.pid" ]; then
                local pid=$(cat "${AGENT_HOME}/agent.pid")
                kill $pid 2>/dev/null
                rm -f "${AGENT_HOME}/agent.pid"
            fi
            
            if [ -f "${AGENT_HOME}/heartbeat.pid" ]; then
                local hb_pid=$(cat "${AGENT_HOME}/heartbeat.pid")
                kill $hb_pid 2>/dev/null
                rm -f "${AGENT_HOME}/heartbeat.pid"
            fi
            
            log_info "Agent 已停止"
            ;;
            
        *)
            echo "用法: $0 {start|execute|test|stop}"
            echo ""
            echo "命令:"
            echo "  start    - 启动Agent服务"
            echo "  execute  - 执行归档任务（JSON参数）"
            echo "  test     - 测试连接和配置"
            echo "  stop     - 停止Agent服务"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 