<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Oracle 分布式归档系统{% endblock %}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav {
            background-color: #34495e;
            padding: 0 2rem;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
        }
        
        .nav li {
            margin-right: 2rem;
        }
        
        .nav a {
            color: white;
            text-decoration: none;
            padding: 1rem 0;
            display: block;
            transition: color 0.3s;
        }
        
        .nav a:hover {
            color: #3498db;
        }
        
        .nav a.active {
            color: #3498db;
            border-bottom: 2px solid #3498db;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-card p {
            opacity: 0.9;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            text-align: left;
            padding: 0.75rem;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            background-color: #6c757d;
            color: white;
        }
        
        .btn:hover {
            background-color: #5a6268;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: #27ae60;
            color: white;
        }
        
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        
        .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-online {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-running {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-dispatching {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .status-completed_with_warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-completed_no_delete {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            width: 600px;
            border-radius: 8px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        /* 表单样式 */
        input[type="text"],
        input[type="number"],
        input[type="email"],
        select,
        textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.25rem;
            font-weight: 500;
            color: #495057;
        }
    </style>
</head>
<body>
    <header class="header">
        <h1>Oracle 分布式归档系统</h1>
    </header>
    
    <nav class="nav">
        <ul>
            <li><a href="/" {% if request.endpoint == 'dashboard' %}class="active"{% endif %}>仪表板</a></li>
            <li><a href="/agents" {% if request.endpoint == 'agents' %}class="active"{% endif %}>Agent管理</a></li>
            <li><a href="/tables" {% if request.endpoint == 'tables' %}class="active"{% endif %}>表配置</a></li>
            <li><a href="/tasks" {% if request.endpoint == 'tasks' %}class="active"{% endif %}>任务管理</a></li>
        </ul>
    </nav>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </div>
    
    <script>
        // 自动刷新页面（可选）
        // setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html> 