from flask import request, jsonify, current_app
from app import db
from app.models import ArchiveTask, SystemLog
from app.api import api_bp
from app.services.scheduler_service import scheduler_service

@api_bp.route('/scheduler/status', methods=['GET'])
def get_scheduler_status():
    """获取调度器状态"""
    try:
        status = {
            'is_running': scheduler_service.is_running,
            'total_jobs': len(scheduler_service.get_all_jobs()) if scheduler_service.is_running else 0
        }
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'获取调度器状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取调度器状态失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/start', methods=['POST'])
def start_scheduler():
    """启动调度器"""
    try:
        if scheduler_service.is_running:
            return jsonify({
                'success': False,
                'message': '调度器已经在运行'
            }), 400
        
        scheduler_service.start()
        
        SystemLog.log_info('scheduler', '手动启动调度器')
        
        return jsonify({
            'success': True,
            'message': '调度器启动成功'
        })
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'启动调度器失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'启动调度器失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/stop', methods=['POST'])
def stop_scheduler():
    """停止调度器"""
    try:
        if not scheduler_service.is_running:
            return jsonify({
                'success': False,
                'message': '调度器未运行'
            }), 400
        
        scheduler_service.stop()
        
        SystemLog.log_info('scheduler', '手动停止调度器')
        
        return jsonify({
            'success': True,
            'message': '调度器停止成功'
        })
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'停止调度器失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'停止调度器失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/jobs', methods=['GET'])
def get_scheduled_jobs():
    """获取所有定时任务"""
    try:
        jobs = scheduler_service.get_all_jobs()
        
        return jsonify({
            'success': True,
            'data': jobs
        })
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'获取定时任务列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取定时任务列表失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/jobs/<int:task_id>', methods=['GET'])
def get_job_status(task_id):
    """获取特定任务的调度状态"""
    try:
        job_status = scheduler_service.get_job_status(task_id)
        
        if job_status:
            return jsonify({
                'success': True,
                'data': job_status
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务未找到或未启用调度'
            }), 404
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'获取任务调度状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取任务调度状态失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/jobs/<int:task_id>/pause', methods=['POST'])
def pause_job(task_id):
    """暂停定时任务"""
    try:
        success = scheduler_service.pause_job(task_id)
        
        if success:
            SystemLog.log_info('scheduler', f'暂停定时任务: {task_id}')
            return jsonify({
                'success': True,
                'message': '任务暂停成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务暂停失败'
            }), 400
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'暂停定时任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'暂停定时任务失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/jobs/<int:task_id>/resume', methods=['POST'])
def resume_job(task_id):
    """恢复定时任务"""
    try:
        success = scheduler_service.resume_job(task_id)
        
        if success:
            SystemLog.log_info('scheduler', f'恢复定时任务: {task_id}')
            return jsonify({
                'success': True,
                'message': '任务恢复成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务恢复失败'
            }), 400
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'恢复定时任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'恢复定时任务失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/jobs/<int:task_id>/reload', methods=['POST'])
def reload_job(task_id):
    """重新加载定时任务"""
    try:
        task = ArchiveTask.query.get_or_404(task_id)
        
        success = scheduler_service.update_scheduled_task(task)
        
        if success:
            SystemLog.log_info('scheduler', f'重新加载定时任务: {task.name}')
            return jsonify({
                'success': True,
                'message': '任务重新加载成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务重新加载失败'
            }), 400
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'重新加载定时任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'重新加载定时任务失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/reload-all', methods=['POST'])
def reload_all_jobs():
    """重新加载所有定时任务"""
    try:
        # 停止调度器
        if scheduler_service.is_running:
            scheduler_service.stop()
        
        # 重新启动调度器
        scheduler_service.start()
        
        SystemLog.log_info('scheduler', '重新加载所有定时任务')
        
        return jsonify({
            'success': True,
            'message': '所有任务重新加载成功'
        })
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'重新加载所有定时任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'重新加载所有定时任务失败: {str(e)}'
        }), 500

@api_bp.route('/scheduler/validate-cron', methods=['POST'])
def validate_cron():
    """验证Cron表达式"""
    try:
        data = request.get_json()
        cron_expression = data.get('cron_expression')
        
        if not cron_expression:
            return jsonify({
                'success': False,
                'message': '缺少Cron表达式'
            }), 400
        
        try:
            from apscheduler.triggers.cron import CronTrigger
            trigger = CronTrigger.from_crontab(cron_expression)
            
            # 获取下次运行时间
            from datetime import datetime
            next_run = trigger.get_next_fire_time(None, datetime.now())
            
            return jsonify({
                'success': True,
                'message': 'Cron表达式有效',
                'data': {
                    'cron_expression': cron_expression,
                    'next_run_time': next_run.isoformat() if next_run else None,
                    'description': str(trigger)
                }
            })
            
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': f'无效的Cron表达式: {str(e)}'
            }), 400
        
    except Exception as e:
        SystemLog.log_error('scheduler', f'验证Cron表达式失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'验证Cron表达式失败: {str(e)}'
        }), 500
