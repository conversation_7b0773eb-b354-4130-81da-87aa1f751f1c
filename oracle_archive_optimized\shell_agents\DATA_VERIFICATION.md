# 数据完整性验证机制

## 概述

增强版的 Oracle Archive Agent 实现了与原脚本相同的**数据完整性验证机制**，确保归档过程的安全性和可靠性。

## 核心验证流程

### 1. 归档前统计（Count Before Export）

在导出数据之前，系统会先执行 COUNT 查询，统计符合归档条件的记录数：

```sql
SELECT COUNT(*) FROM schema.table
WHERE date_field < TO_DATE('cutoff_date', 'YYYY-MM-DD');
```

### 2. 数据导出（Export with Logging）

使用 Oracle 的 exp 或 expdp 工具导出数据，并生成详细的导出日志：

```bash
exp user/pass TABLES=schema.table QUERY="WHERE ..." LOG=export.log
```

### 3. 验证导出结果（Verify Export）

从导出日志中提取实际导出的行数，并与预期数量进行比对：

```bash
# 从日志提取: "5806 rows exported"
exported_count=$(grep "rows exported" export.log | awk '{print $1}')
```

### 4. 比对决策（Comparison Decision）

只有当以下条件**全部满足**时，才会执行删除操作：

- ✅ 导出成功完成（exit code = 0）
- ✅ 导出行数与统计行数**完全一致**
- ✅ 文件成功压缩
- ✅ 文件成功上传到备份服务器

任何一个条件不满足，都会**中止删除操作**，保证数据安全。

## 配置选项

### 必要配置

```bash
# 数据验证配置（默认开启，强烈建议保持开启）
VERIFY_BEFORE_DELETE="true"

# 批量删除配置（避免锁表）
BATCH_DELETE_SIZE="5000"
DELETE_SLEEP_TIME="0.1"
```

### 验证失败处理

当数据验证失败时：

1. **不执行删除** - 源数据保持不变
2. **记录详细日志** - 包含期望值和实际值
3. **发送告警邮件** - 通知 DBA 介入处理
4. **任务标记失败** - 在中央控制台显示

## 与原脚本的对比

| 功能特性     | 原脚本        | 增强版 Agent |
| ------------ | ------------- | ------------ |
| 归档前统计   | ✅ 支持       | ✅ 支持      |
| 导出验证     | ✅ 支持       | ✅ 支持      |
| 批量删除     | ✅ 5000 行/批 | ✅ 可配置    |
| 邮件通知     | ✅ mailx      | ✅ mailx     |
| 统计信息收集 | ✅ 支持       | ✅ 自动      |
| 分布式支持   | ❌ 单机       | ✅ 多 Agent  |
| 任务调度     | ❌ cron       | ✅ 中央调度  |
| 实时监控     | ❌ 无         | ✅ Web 界面  |

## 安全保障

### 1. 多重验证

- 数据行数验证
- 文件大小验证
- 上传完整性验证

### 2. 事务保护

```sql
BEGIN
    LOOP
        DELETE ... WHERE ROWNUM <= 5000;
        IF SQL%NOTFOUND THEN EXIT; END IF;
        COMMIT;  -- 每批次提交
    END LOOP;
END;
```

### 3. 回滚机制

- 保留导出文件和日志
- 支持从备份恢复
- 详细的操作审计

## 测试验证

使用测试脚本验证功能：

```bash
# 基础测试（配置和连接）
./test_archive.sh

# 完整测试（包括数据验证）
./test_archive.sh full
```

## 故障处理

### 验证失败

1. 检查导出日志中的错误信息
2. 确认表结构没有变化
3. 检查是否有其他进程在操作数据
4. 手动执行 COUNT 查询验证

### 常见问题

**Q: 为什么导出行数与统计不一致？**
A: 可能原因：

- 统计和导出之间有新数据插入
- 有其他进程在删除数据
- 导出过程中遇到锁等待

**Q: 可以关闭验证吗？**
A: 技术上可以（设置 VERIFY_BEFORE_DELETE=false），但**强烈不建议**，这会带来数据丢失风险。

**Q: 验证失败后如何处理？**
A:

1. 保留现场，不要手动删除数据
2. 检查日志确认具体原因
3. 解决问题后重新执行归档任务

## 最佳实践

1. **始终开启数据验证** - 这是防止数据丢失的最后防线
2. **定期测试验证功能** - 确保验证机制正常工作
3. **监控验证失败事件** - 及时发现潜在问题
4. **保留归档日志** - 用于事后审计和问题排查
5. **设置合理的批量大小** - 平衡性能和锁等待

## 总结

增强版 Agent 完整保留了原脚本的数据验证机制，并在此基础上增加了更多的安全特性和监控能力。这确保了归档操作的**数据完整性**和**可追溯性**。
