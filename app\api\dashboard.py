from flask import request, jsonify, current_app
from app import db
from app.models import DatabaseConnection, ArchiveTask, TaskExecution, SystemLog
from app.api import api_bp
from datetime import datetime, timedelta
from sqlalchemy import func

@api_bp.route('/dashboard/overview', methods=['GET'])
def get_dashboard_overview():
    """获取仪表板概览数据"""
    try:
        # 数据库连接统计
        total_connections = DatabaseConnection.query.count()
        active_connections = DatabaseConnection.query.filter_by(is_active=True).count()
        
        # 归档任务统计
        total_tasks = ArchiveTask.query.count()
        active_tasks = ArchiveTask.query.filter_by(is_active=True).count()
        scheduled_tasks = ArchiveTask.query.filter_by(
            is_active=True, 
            schedule_enabled=True
        ).count()
        
        # 执行统计（最近24小时）
        last_24h = datetime.utcnow() - timedelta(hours=24)
        executions_24h = TaskExecution.query.filter(
            TaskExecution.start_time >= last_24h
        ).count()
        
        success_24h = TaskExecution.query.filter(
            TaskExecution.start_time >= last_24h,
            TaskExecution.status == 'success'
        ).count()
        
        failed_24h = TaskExecution.query.filter(
            TaskExecution.start_time >= last_24h,
            TaskExecution.status == 'failed'
        ).count()
        
        running_now = TaskExecution.query.filter_by(status='running').count()
        
        # 计算成功率
        success_rate_24h = 0
        if executions_24h > 0:
            success_rate_24h = round((success_24h / executions_24h) * 100, 2)
        
        return jsonify({
            'success': True,
            'data': {
                'connections': {
                    'total': total_connections,
                    'active': active_connections
                },
                'tasks': {
                    'total': total_tasks,
                    'active': active_tasks,
                    'scheduled': scheduled_tasks
                },
                'executions_24h': {
                    'total': executions_24h,
                    'success': success_24h,
                    'failed': failed_24h,
                    'running': running_now,
                    'success_rate': success_rate_24h
                }
            }
        })
        
    except Exception as e:
        SystemLog.log_error('dashboard', f'获取仪表板概览失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取仪表板概览失败: {str(e)}'
        }), 500

@api_bp.route('/dashboard/execution-chart', methods=['GET'])
def get_execution_chart_data():
    """获取执行趋势图表数据"""
    try:
        days = request.args.get('days', 7, type=int)
        
        # 生成日期范围
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days-1)
        
        # 查询每日执行统计
        daily_stats = []
        current_date = start_date
        
        while current_date <= end_date:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            
            total = TaskExecution.query.filter(
                TaskExecution.start_time >= day_start,
                TaskExecution.start_time <= day_end
            ).count()
            
            success = TaskExecution.query.filter(
                TaskExecution.start_time >= day_start,
                TaskExecution.start_time <= day_end,
                TaskExecution.status == 'success'
            ).count()
            
            failed = TaskExecution.query.filter(
                TaskExecution.start_time >= day_start,
                TaskExecution.start_time <= day_end,
                TaskExecution.status == 'failed'
            ).count()
            
            daily_stats.append({
                'date': current_date.isoformat(),
                'total': total,
                'success': success,
                'failed': failed
            })
            
            current_date += timedelta(days=1)
        
        return jsonify({
            'success': True,
            'data': daily_stats
        })
        
    except Exception as e:
        SystemLog.log_error('dashboard', f'获取执行趋势数据失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取执行趋势数据失败: {str(e)}'
        }), 500

@api_bp.route('/dashboard/task-status', methods=['GET'])
def get_task_status_data():
    """获取任务状态分布数据"""
    try:
        # 按任务状态统计
        status_stats = db.session.query(
            ArchiveTask.is_active,
            ArchiveTask.schedule_enabled,
            func.count(ArchiveTask.id).label('count')
        ).group_by(
            ArchiveTask.is_active,
            ArchiveTask.schedule_enabled
        ).all()
        
        # 处理统计结果
        active_scheduled = 0
        active_manual = 0
        inactive = 0
        
        for stat in status_stats:
            is_active, schedule_enabled, count = stat
            if is_active:
                if schedule_enabled:
                    active_scheduled += count
                else:
                    active_manual += count
            else:
                inactive += count
        
        return jsonify({
            'success': True,
            'data': {
                'active_scheduled': active_scheduled,
                'active_manual': active_manual,
                'inactive': inactive
            }
        })
        
    except Exception as e:
        SystemLog.log_error('dashboard', f'获取任务状态分布失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取任务状态分布失败: {str(e)}'
        }), 500

@api_bp.route('/dashboard/recent-activities', methods=['GET'])
def get_recent_activities():
    """获取最近活动"""
    try:
        limit = request.args.get('limit', 10, type=int)
        
        # 获取最近的系统日志
        recent_logs = SystemLog.query.order_by(
            SystemLog.created_at.desc()
        ).limit(limit).all()
        
        # 获取最近的任务执行
        recent_executions = TaskExecution.query.order_by(
            TaskExecution.start_time.desc()
        ).limit(limit).all()
        
        # 合并并按时间排序
        activities = []
        
        for log in recent_logs:
            activities.append({
                'type': 'log',
                'time': log.created_at.isoformat(),
                'level': log.level,
                'module': log.module,
                'message': log.message,
                'details': log.details
            })
        
        for execution in recent_executions:
            activities.append({
                'type': 'execution',
                'time': execution.start_time.isoformat(),
                'task_name': execution.archive_task.name if execution.archive_task else '未知任务',
                'status': execution.status,
                'duration': execution.duration_formatted,
                'records_exported': execution.records_exported,
                'records_deleted': execution.records_deleted
            })
        
        # 按时间倒序排序
        activities.sort(key=lambda x: x['time'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': activities[:limit]
        })
        
    except Exception as e:
        SystemLog.log_error('dashboard', f'获取最近活动失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取最近活动失败: {str(e)}'
        }), 500

@api_bp.route('/dashboard/system-health', methods=['GET'])
def get_system_health():
    """获取系统健康状态"""
    try:
        import psutil
        import os
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        
        # 数据库文件大小
        db_path = current_app.config.get('ARCHIVE_CONFIG', {}).get('database', {}).get('url', '')
        db_size = 0
        if 'sqlite:///' in db_path:
            db_file = db_path.replace('sqlite:///', '')
            if os.path.exists(db_file):
                db_size = os.path.getsize(db_file)
        
        # 检查数据库连接
        db_healthy = True
        try:
            db.session.execute('SELECT 1')
        except Exception:
            db_healthy = False
        
        return jsonify({
            'success': True,
            'data': {
                'cpu_percent': round(cpu_percent, 2),
                'memory_percent': round(memory_percent, 2),
                'disk_percent': round(disk_percent, 2),
                'database_size': db_size,
                'database_healthy': db_healthy,
                'uptime': '运行中'  # 可以实现更精确的运行时间计算
            }
        })
        
    except ImportError:
        # 如果没有安装psutil，返回基本信息
        return jsonify({
            'success': True,
            'data': {
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_percent': 0,
                'database_size': 0,
                'database_healthy': True,
                'uptime': '运行中',
                'note': '需要安装psutil包以获取详细系统信息'
            }
        })
        
    except Exception as e:
        SystemLog.log_error('dashboard', f'获取系统健康状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取系统健康状态失败: {str(e)}'
        }), 500
