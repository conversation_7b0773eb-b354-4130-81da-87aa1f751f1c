#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库归档管理平台启动脚本
"""

import os
import sys
from app import create_app

def create_default_config(config_file):
    """创建默认配置文件"""
    default_config = """app:
  name: "数据库归档管理平台"
  host: "0.0.0.0"
  port: 8080
  debug: true
  secret_key: "dev-secret-key-change-in-production"

database:
  url: "sqlite:///./data/archive_platform.db"
  echo: false

logging:
  level: "INFO"
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

security:
  encryption_key: "dev-encryption-key-change-in-production"
  password_salt: "dev-password-salt-change-in-production"

archive:
  temp_dir: "temp"
  backup_dir: "backups"
  max_concurrent_tasks: 3
  batch_delete_size: 5000
  compression_level: 6

notification:
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    use_tls: true
    from_address: ""

  wechat:
    enabled: false
    webhook_url: ""

  dingtalk:
    enabled: false
    webhook_url: ""
    secret: ""

upload:
  default_type: "local"  # local, sftp, ftp
  retry_count: 3
  timeout: 300

  sftp:
    host: ""
    port: 22
    username: ""
    password: ""
    remote_path: "/backup"

  ftp:
    host: ""
    port: 21
    username: ""
    password: ""
    remote_path: "/backup"
    passive: true
"""

    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(default_config)
    print(f"✓ 创建默认配置文件: {config_file}")

def main():
    """主函数"""
    # 确保必要的目录存在
    directories = ['data', 'logs', 'temp', 'backups', 'config']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

    # 检查配置文件是否存在，如果不存在则创建默认配置
    config_file = 'config/archive_config.yaml'
    if not os.path.exists(config_file):
        create_default_config(config_file)

    # 创建Flask应用
    app = create_app()
    
    # 获取配置
    config = app.config.get('ARCHIVE_CONFIG', {})
    app_config = config.get('app', {})
    
    host = app_config.get('host', '0.0.0.0')
    port = app_config.get('port', 8080)
    debug = app_config.get('debug', False)
    
    print(f"数据库归档管理平台启动中...")
    print(f"访问地址: http://{host}:{port}")
    print(f"调试模式: {'开启' if debug else '关闭'}")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
    except Exception as e:
        print(f"启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
