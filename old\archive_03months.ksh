#!/bin/sh
. $HOME/.bash_profile
> /home/<USER>/dba_cmd2/all.log
cd /home/<USER>/dba_cmd2
/home/<USER>/dba_cmd2/select_count.ksh > /home/<USER>/dba_cmd2/select_row.tmp
cat /home/<USER>/dba_cmd2/select_row.tmp >>/home/<USER>/dba_cmd2/all.log
INI=/home/<USER>/dba_cmd2/tname_33.ini
HOSTS=`cat $INI |awk '{print $1}'`
DATE=`date +%Y%m%d-%H%M%S`
for HOST in $HOSTS
do
echo "====="  $HOST "===="

exp PNACGECDA1/pnacgecadm123 TABLES=$HOST  query=\"WHERE to_char\(TIME_STAMP\,\'yyyy\-\mm\-dd\'\)\< to\_char\(ADD\_MONTHS \(SYSDATE\, \-03\)\,\'yyyy\-\mm\-dd\'\)\" FILE=/PNAEC_BK/PNAEC-DATA/"$HOST"-$DATE.dmp log=/PNAEC_BK/PNAEC-DATA/"$HOST"_$DATE.log  indexes=n grants=n statistics=NONE
cat /PNAEC_BK/PNAEC-DATA/"$HOST"_$DATE.log >> /home/<USER>/dba_cmd2/all.log
done
> /home/<USER>/dba_cmd2/error.log
> /home/<USER>/dba_cmd2/drop.sql
> /home/<USER>/dba_cmd2/drop.tmp
> /home/<USER>/dba_cmd2/analyze.sql
> /home/<USER>/dba_cmd2/analyze.tmp
cat $INI |awk '{print $1}' | while read HOST
do
  cat /home/<USER>/dba_cmd2/select_row.tmp |grep aa |awk '{print $2,$3}' | while read tname rown
    do
     if [[ "$tname" == "$HOST" ]] ;then
          cat /PNAEC_BK/PNAEC-DATA/"$HOST"_"$DATE".log | grep exporting | awk '{print $6}' | while read expn
             do
               if [ "$expn" == "$rown" ];then
               if [ "$expn" -ne "0" ] ; then
               echo "BEGIN" >> /home/<USER>/dba_cmd2/drop.sql
               echo "   loop" >> /home/<USER>/dba_cmd2/drop.sql
               echo "     DELETE FROM PNACGECDA1.$tname " >> /home/<USER>/dba_cmd2/drop.sql
               echo "    WHERE to_char(TIME_STAMP,'yyyy-mm-dd') < to_char(ADD_MONTHS (SYSDATE, -03),'yyyy-mm-dd')" >> /home/<USER>/dba_cmd2/drop.sql
               echo "            AND   ROWNUM < 5000;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "     IF SQL%NOTFOUND THEN" >> /home/<USER>/dba_cmd2/drop.sql
               echo "        EXIT;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "     END IF;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "     COMMIT;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "   END LOOP;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "   COMMIT;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "   " >> /home/<USER>/dba_cmd2/drop.sql
               echo "   EXCEPTION" >> /home/<USER>/dba_cmd2/drop.sql
               echo "     WHEN NO_DATA_FOUND THEN" >> /home/<USER>/dba_cmd2/drop.sql
               echo "       NULL;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "     WHEN OTHERS THEN" >> /home/<USER>/dba_cmd2/drop.sql
               echo "       RAISE;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "END;" >> /home/<USER>/dba_cmd2/drop.sql
               echo "/" >> /home/<USER>/dba_cmd2/drop.sql
     echo "EXEC DBMS_STATS.GATHER_TABLE_STATS(OWNNAME =>'PNACGECDA1',TABNAME =>'$tname');">> /home/<USER>/dba_cmd2/analyze.sql
                       fi
                else
                  echo "$tname"_"$T_DATE"_"fail" >> /home/<USER>/dba_cmd2/error.log
                fi
              done
                 fi
              done
              done

cd /PNAEC_BK/PNAEC-DATA
tar -czvf /PNAEC_BK/PNAEC-DATA/PNACGECDA1_03M_$DATE.tar.gz *$DATE.* && rm -f *$DATE.dmp
mv /PNAEC_BK/PNAEC-DATA/PNACGECDA1_03M_$DATE.tar.gz /PNAEC_BK/nbubackup/
/home/<USER>/dba_cmd2/drop.ksh > /home/<USER>/dba_cmd2/drop.tmp
/home/<USER>/dba_cmd2/analyze.ksh > /home/<USER>/dba_cmd2/analyze.tmp
cat /home/<USER>/dba_cmd2/drop.tmp >> /home/<USER>/dba_cmd2/all.log
cat /home/<USER>/dba_cmd2/analyze.tmp >> /home/<USER>/dba_cmd2/all.log
cat /home/<USER>/dba_cmd2/error.log >> /home/<USER>/dba_cmd2/all.log

mailx -s "$ORACLE_SID 03MONTHS PNACGECDA1 DATA ARCHIVE" <EMAIL> </home/<USER>/dba_cmd2/all.log  -- -f <EMAIL>
