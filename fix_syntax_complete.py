#!/usr/bin/env python3
"""
完整修复strm.py中的所有语法错误
"""

def fix_file():
    with open('strm.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 记录修复的行
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # 修复第661行附近的缩进问题
        if 'self.stats[\'files_downloaded\'] += 1' in line and i > 650:
            # 确保正确的缩进（应该在try块内）
            fixed_lines.append('                        self.stats[\'files_downloaded\'] += 1\n')
            i += 1
            
            # 跳过错误的空行和注释
            while i < len(lines) and (lines[i].strip() == '' or 'progress_interval' in lines[i]):
                if 'progress_interval' in lines[i]:
                    # 重新写入正确的进度显示代码
                    fixed_lines.append('                        \n')
                    fixed_lines.append('                        # 显示下载进度\n')
                    fixed_lines.append('                        progress_interval = self.config.get(\'advanced\', {}).get(\'progress_interval\', 10)\n')
                    fixed_lines.append('                        if self.stats[\'files_downloaded\'] % progress_interval == 0:\n')
                    fixed_lines.append('                            logger.info(f"下载进度: 已完成 {self.stats[\'files_downloaded\']} 个文件")\n')
                    fixed_lines.append('                        else:\n')
                    fixed_lines.append('                            logger.info(f"下载完成 [{self.stats[\'files_downloaded\']}]: {local_path.name}")\n')
                    # 跳过错误的进度显示代码
                    while i < len(lines) and ('progress_interval' in lines[i] or 'logger.info' in lines[i] or lines[i].strip() == ''):
                        i += 1
                    i -= 1  # 回退一行，因为外层循环会+1
                    break
                i += 1
        else:
            fixed_lines.append(line)
        
        i += 1
    
    # 写回文件
    with open('strm.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("所有语法错误已修复")

if __name__ == "__main__":
    fix_file() 