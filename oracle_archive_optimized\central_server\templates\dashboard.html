{% extends "base.html" %}

{% block title %}仪表板 - Oracle 分布式归档系统{% endblock %}

{% block content %}
<h1>系统概览</h1>

<div class="stats-grid">
    <div class="stat-card">
        <h3>{{ stats.total_agents }}</h3>
        <p>总Agent数</p>
    </div>
    <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #229954);">
        <h3>{{ stats.online_agents }}</h3>
        <p>在线Agent</p>
    </div>
    <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
        <h3>{{ stats.enabled_tables }}</h3>
        <p>活跃归档表</p>
    </div>
    <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #d68910);">
        <h3>{{ stats.today_tasks }}</h3>
        <p>今日任务</p>
    </div>
</div>

<div class="card">
    <h2>任务统计</h2>
    <div style="display: flex; gap: 2rem; margin-bottom: 1rem;">
        <div>
            <strong>已完成:</strong> <span class="status status-completed">{{ stats.completed_tasks }}</span>
        </div>
        <div>
            <strong>失败:</strong> <span class="status status-failed">{{ stats.failed_tasks }}</span>
        </div>
        <div>
            <strong>存储使用:</strong> {{ (stats.storage_used / 1024 / 1024 / 1024)|round(2) }} GB
        </div>
    </div>
</div>

<div class="card">
    <h2>快速操作</h2>
    <div style="display: flex; gap: 1rem;">
        <button class="btn btn-primary" onclick="createAllTasks()">创建所有任务</button>
        <button class="btn btn-success" onclick="checkAgentStatus()">检查Agent状态</button>
        <a href="/agents" class="btn btn-primary">管理Agent</a>
        <a href="/tables" class="btn btn-primary">配置归档表</a>
    </div>
</div>

<div class="card">
    <h2>系统状态</h2>
    <table>
        <thead>
            <tr>
                <th>组件</th>
                <th>状态</th>
                <th>说明</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>中央控制服务</td>
                <td><span class="status status-online">运行中</span></td>
                <td>Web服务正常</td>
            </tr>
            <tr>
                <td>调度器</td>
                <td><span class="status status-online">运行中</span></td>
                <td>自动任务调度已启用</td>
            </tr>
            <tr>
                <td>存储服务</td>
                <td><span class="status status-online">正常</span></td>
                <td>本地存储可用</td>
            </tr>
        </tbody>
    </table>
</div>

<script>
function createAllTasks() {
    if (confirm('确定要为所有启用的表创建归档任务吗？')) {
        fetch('/api/task/create', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`成功创建 ${data.task_count} 个任务`);
                location.reload();
            } else {
                alert('创建任务失败: ' + data.error);
            }
        });
    }
}

function checkAgentStatus() {
    alert('正在检查所有Agent状态...');
    // 这里可以添加实际的状态检查逻辑
    location.reload();
}
</script>
{% endblock %} 