#!/usr/bin/env python3
"""
快速修复strm.py中的语法错误
"""

def fix_file():
    with open('strm.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复已知的缩进问题
    fixes = [
        # 修复第584行的缩进问题
        ('                        self.stats[\'strm_created\'] += 1\n        \n        # 显示进度信息',
         '                self.stats[\'strm_created\'] += 1\n                \n                # 显示进度信息'),
        
        # 修复下载文件的缩进问题  
        ('                                            self.stats[\'files_downloaded\'] += 1\n                    \n                    # 显示下载进度',
         '                        self.stats[\'files_downloaded\'] += 1\n                        \n                        # 显示下载进度'),
    ]
    
    for old, new in fixes:
        content = content.replace(old, new)
    
    # 写回文件
    with open('strm.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("语法错误已修复")

if __name__ == "__main__":
    fix_file() 