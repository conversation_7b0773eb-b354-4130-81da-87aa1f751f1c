#!/bin/bash
#############################################################################
# Oracle Archive Agent 增强版配置文件
# 包含数据验证和邮件通知功能
#############################################################################

# Agent标识
AGENT_ID="oracle_agent_01"

# Oracle数据库配置
ORACLE_HOME="/u01/app/oracle/product/19.0.0/dbhome_1"
ORACLE_SID="ORCL"
DB_USER="archive_user"
DB_PASS="archive_password"
NLS_LANG="AMERICAN_AMERICA.AL32UTF8"

# 中央控制服务器
CENTRAL_SERVER="http://central-server.example.com:8080"

# 导出方法配置
EXPORT_METHOD="exp"          # exp（传统导出）或 expdp（数据泵）
DATA_PUMP_DIR="DATA_PUMP_DIR"  # 使用expdp时的目录对象

# 数据验证配置（重要！）
VERIFY_BEFORE_DELETE="true"  # 删除前必须验证数据完整性
ALLOW_PARTIAL_DELETE="false" # 是否允许部分删除（不建议）

# 上传配置
UPLOAD_TYPE="scp"  # scp, sftp, http, local
UPLOAD_SERVER="backup-server.example.com"
UPLOAD_PATH="/backup/oracle_archives"
UPLOAD_USER="backup_user"

# 压缩配置
COMPRESSION_TYPE="tar.gz"    # tar.gz, gzip, zip
COMPRESSION_LEVEL="6"        # 1-9

# 归档配置
DELETE_AFTER_ARCHIVE="true"  # 是否在归档后删除原数据
BATCH_DELETE_SIZE="5000"     # 批量删除大小（避免锁表）
DELETE_SLEEP_TIME="0.1"      # 批次之间的暂停时间（秒）

# 邮件通知配置
SEND_NOTIFICATION="true"
NOTIFY_EMAIL="<EMAIL> <EMAIL>"  # 空格分隔多个邮箱
SMTP_FROM="<EMAIL>"
SMTP_SERVER="smtp.example.com"
SMTP_PORT="25"

# Agent配置
HEARTBEAT_INTERVAL="60"      # 心跳间隔（秒）
LOG_LEVEL="INFO"             # DEBUG, INFO, ERROR
LOG_RETENTION_DAYS="30"      # 日志保留天数

# 性能配置
PARALLEL_JOBS="1"            # 并行任务数
MAX_RETRY="3"                # 最大重试次数
RETRY_DELAY="300"            # 重试延迟（秒）

# 安全配置
MASK_PASSWORD="true"         # 日志中屏蔽密码
ENCRYPT_EXPORT="false"       # 是否加密导出文件
ENCRYPTION_KEY=""            # 加密密钥

# 监控配置
ENABLE_METRICS="true"        # 启用性能指标收集
METRICS_INTERVAL="300"       # 指标收集间隔（秒）

# 高级配置
CUSTOM_WHERE_CLAUSE=""       # 自定义WHERE条件（追加）
EXCLUDE_TABLES=""            # 排除的表（逗号分隔）
INCLUDE_INDEXES="false"      # 是否包含索引
INCLUDE_GRANTS="false"       # 是否包含权限 