/* 数据库归档管理平台 - 主样式文件 */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 主要布局 */
.main-content {
    flex: 1;
    padding: 0;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* 卡片和组件样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stats-card .stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 按钮增强样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* 表格样式 */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* 连接卡片样式 */
.connection-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    padding: 20px;
    transition: box-shadow 0.3s ease;
}

.connection-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.connection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.connection-header h3 {
    margin: 0;
    color: #333;
}

.connection-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.connection-status.active {
    background-color: #d4edda;
    color: #155724;
}

.connection-status.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.connection-info {
    margin-bottom: 15px;
}

.connection-info p {
    margin: 5px 0;
    font-size: 14px;
}

.connection-actions {
    display: flex;
    gap: 10px;
}

/* 对话框样式 */
.dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.dialog-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

.dialog-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dialog-header h3 {
    margin: 0;
}

.dialog-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.dialog-close:hover {
    color: #333;
}

.dialog-body {
    padding: 20px;
}

.dialog-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #409EFF;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check input[type="checkbox"] {
    width: auto;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px;
    color: #999;
}

.empty-state p {
    margin-bottom: 20px;
    font-size: 16px;
}

/* 消息提示样式 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
    z-index: 2000;
    animation: slideIn 0.3s ease;
}

.message-success {
    background-color: #28a745;
}

.message-error {
    background-color: #dc3545;
}

.message-info {
    background-color: #17a2b8;
}

.message-warning {
    background-color: #ffc107;
    color: #333;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .connection-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .connection-actions {
        flex-wrap: wrap;
    }
    
    .dialog-content {
        width: 95%;
    }
}

/* 加载动画和遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 状态标签 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.status-badge.status-error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-badge.status-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.status-badge.status-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: var(--info-color);
}

/* 系统设置页面样式 */
.settings-nav .list-group-item {
    border: none;
    border-radius: 0.375rem;
    padding: 0.875rem 1rem;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
    position: relative;
}

.settings-nav .list-group-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color);
    transform: translateX(4px);
}

.settings-nav .list-group-item.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

.settings-nav .list-group-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #ffc107;
    border-radius: 0 2px 2px 0;
}

.settings-nav .list-group-item i {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
}

.settings-nav .list-group-item small {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

/* 设置表单样式 */
.settings-form .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.settings-form .form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.375rem;
    line-height: 1.4;
}

.settings-form .form-check-label {
    font-weight: 500;
    margin-left: 0.5rem;
    cursor: pointer;
}

.settings-form .form-check-label strong {
    display: block;
    margin-bottom: 0.25rem;
}

.settings-form .input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
    font-weight: 500;
}

.settings-form .form-control:focus,
.settings-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.settings-form h6 {
    color: var(--primary-color);
    font-weight: 600;
    border-bottom: 2px solid rgba(13, 110, 253, 0.1);
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

/* 设置卡片 */
.settings-card {
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.settings-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.settings-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
    position: relative;
}

.settings-card .card-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--info-color) 100%);
}

.settings-card .card-header h5 {
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.settings-card .card-header small {
    color: #6c757d;
    font-weight: 400;
}

.settings-card .card-body {
    padding: 2rem 1.5rem;
    background: white;
}

/* 配置预览 */
.config-preview {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #e1bee7;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
}

.config-preview .preview-label {
    font-weight: 600;
    color: #4a148c;
    margin-bottom: 0.5rem;
}

.config-preview .preview-value {
    font-family: 'Courier New', monospace;
    background: white;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #d1c4e9;
}

/* 开关样式 */
.form-switch .form-check-input {
    width: 2.5rem;
    height: 1.25rem;
    background-color: #dee2e6;
    border: none;
    border-radius: 2rem;
    transition: all 0.3s ease;
}

.form-switch .form-check-input:checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

/* 进度条样式 */
.progress-modern {
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-modern .progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.6s ease;
}

/* 统计卡片增强 */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stat-card.stat-primary {
    border-left-color: var(--primary-color);
}

.stat-card.stat-success {
    border-left-color: var(--success-color);
}

.stat-card.stat-warning {
    border-left-color: var(--warning-color);
}

.stat-card.stat-danger {
    border-left-color: var(--danger-color);
}

.stat-card .stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.stat-card .stat-number {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--dark-color);
    color: white;
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* 模态框增强 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .settings-nav {
        margin-bottom: 1rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #343a40;
        --dark-color: #f8f9fa;
    }

    body {
        background-color: #121212;
        color: #f8f9fa;
    }

    .card {
        background-color: #1e1e1e;
        border-color: #343a40;
    }

    .table {
        background-color: #1e1e1e;
        color: #f8f9fa;
    }

    .form-control {
        background-color: #2d2d2d;
        border-color: #495057;
        color: #f8f9fa;
    }

    .form-control:focus {
        background-color: #2d2d2d;
        border-color: var(--primary-color);
        color: #f8f9fa;
    }
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 0;
}

.toolbar h2 {
    margin: 0;
    color: #333;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.table tr:hover {
    background-color: #f8f9fa;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.error {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.info {
    background-color: #d1ecf1;
    color: #0c5460;
}
