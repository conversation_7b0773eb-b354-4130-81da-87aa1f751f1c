#!/bin/bash
#############################################################################
# Oracle Archive Agent 测试脚本
# 用于测试归档功能和数据验证
#############################################################################

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 测试配置
test_config() {
    echo_step "测试配置文件..."
    
    if [ -f "${SCRIPT_DIR}/agent.conf" ]; then
        echo_info "配置文件存在"
        source "${SCRIPT_DIR}/agent.conf"
        
        # 检查关键配置
        local required_vars=(
            "ORACLE_HOME"
            "ORACLE_SID"
            "DB_USER"
            "VERIFY_BEFORE_DELETE"
        )
        
        for var in "${required_vars[@]}"; do
            if [ -z "${!var}" ]; then
                echo_error "缺少配置: $var"
                return 1
            else
                echo_info "$var = ${!var}"
            fi
        done
        
        echo_info "✓ 配置检查通过"
        return 0
    else
        echo_error "配置文件不存在"
        return 1
    fi
}

# 测试数据库连接
test_db_connection() {
    echo_step "测试数据库连接..."
    
    export ORACLE_HOME
    export ORACLE_SID
    export PATH=$ORACLE_HOME/bin:$PATH
    
    local result=$(sqlplus -S "${DB_USER}/${DB_PASS}" <<EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SELECT 'CONNECTION_OK' FROM DUAL;
EXIT;
EOF
)
    
    if echo "$result" | grep -q "CONNECTION_OK"; then
        echo_info "✓ 数据库连接成功"
        
        # 显示数据库信息
        sqlplus -S "${DB_USER}/${DB_PASS}" <<EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT 'Database: ' || ora_database_name FROM DUAL;
SELECT 'Version: ' || version FROM v\$instance;
SELECT 'Time: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') FROM DUAL;
EXIT;
EOF
        return 0
    else
        echo_error "数据库连接失败"
        echo "$result"
        return 1
    fi
}

# 测试统计功能
test_count_records() {
    echo_step "测试记录统计功能..."
    
    local test_table="${1:-TEST_ARCHIVE_TABLE}"
    local test_schema="${2:-$DB_USER}"
    local date_field="${3:-CREATED_DATE}"
    local days="${4:-90}"
    
    echo_info "测试表: ${test_schema}.${test_table}"
    
    # 创建测试表
    sqlplus -S "${DB_USER}/${DB_PASS}" <<EOF
CREATE TABLE ${test_table} (
    ID NUMBER PRIMARY KEY,
    DATA VARCHAR2(100),
    ${date_field} DATE
);

-- 插入测试数据
BEGIN
    -- 插入旧数据（应该被归档）
    FOR i IN 1..100 LOOP
        INSERT INTO ${test_table} VALUES (
            i,
            'Old data ' || i,
            SYSDATE - 120 - i
        );
    END LOOP;
    
    -- 插入新数据（不应该被归档）
    FOR i IN 101..150 LOOP
        INSERT INTO ${test_table} VALUES (
            i,
            'New data ' || i,
            SYSDATE - 30
        );
    END LOOP;
    
    COMMIT;
END;
/
EXIT;
EOF
    
    # 调用count函数
    "${SCRIPT_DIR}/archive_agent_enhanced.sh" count "$test_table" "$test_schema" "$date_field" "$days"
    
    # 清理测试表
    sqlplus -S "${DB_USER}/${DB_PASS}" <<EOF
DROP TABLE ${test_table} PURGE;
EXIT;
EOF
    
    echo_info "✓ 统计功能测试完成"
}

# 测试导出验证
test_export_verify() {
    echo_step "测试导出和验证功能..."
    
    # 创建测试任务JSON
    local test_json='{
        "task_id": 9999,
        "table_name": "TEST_ARCHIVE",
        "schema_name": "'$DB_USER'",
        "date_field": "CREATED_DATE",
        "retention_days": 90,
        "batch_size": 1000
    }'
    
    # 创建测试表并插入数据
    sqlplus -S "${DB_USER}/${DB_PASS}" <<EOF
CREATE TABLE TEST_ARCHIVE (
    ID NUMBER PRIMARY KEY,
    DATA VARCHAR2(100),
    CREATED_DATE DATE
);

BEGIN
    FOR i IN 1..50 LOOP
        INSERT INTO TEST_ARCHIVE VALUES (
            i,
            'Test data ' || i,
            SYSDATE - 100
        );
    END LOOP;
    COMMIT;
END;
/
EXIT;
EOF
    
    # 设置测试模式（不删除数据）
    export DELETE_AFTER_ARCHIVE="false"
    export UPLOAD_TYPE="local"
    export UPLOAD_PATH="/tmp"
    
    # 执行归档任务
    "${SCRIPT_DIR}/archive_agent_enhanced.sh" execute "$test_json"
    
    local exit_code=$?
    
    # 清理测试表
    sqlplus -S "${DB_USER}/${DB_PASS}" <<EOF
DROP TABLE TEST_ARCHIVE PURGE;
EXIT;
EOF
    
    if [ $exit_code -eq 0 ]; then
        echo_info "✓ 导出验证测试通过"
    else
        echo_error "导出验证测试失败"
    fi
    
    return $exit_code
}

# 测试邮件功能
test_email() {
    echo_step "测试邮件发送..."
    
    if command -v mailx >/dev/null 2>&1; then
        echo_info "mailx 可用"
        
        if [ -n "$NOTIFY_EMAIL" ]; then
            echo "Archive Agent Test Email" | mailx -s "Test from $(hostname)" $NOTIFY_EMAIL
            echo_info "测试邮件已发送到: $NOTIFY_EMAIL"
        else
            echo_warn "未配置邮件地址"
        fi
    else
        echo_warn "mailx 不可用，请安装: yum install mailx"
    fi
}

# 主函数
main() {
    echo "Oracle Archive Agent 测试程序"
    echo "============================="
    echo
    
    local all_pass=true
    
    # 1. 测试配置
    if test_config; then
        echo
    else
        all_pass=false
    fi
    
    # 2. 测试数据库连接
    if test_db_connection; then
        echo
    else
        all_pass=false
    fi
    
    # 3. 测试统计功能
    if [ "$1" = "full" ]; then
        if test_count_records; then
            echo
        else
            all_pass=false
        fi
        
        # 4. 测试导出验证
        if test_export_verify; then
            echo
        else
            all_pass=false
        fi
    fi
    
    # 5. 测试邮件
    test_email
    echo
    
    # 总结
    echo "============================="
    if [ "$all_pass" = "true" ]; then
        echo_info "所有测试通过 ✓"
        exit 0
    else
        echo_error "部分测试失败 ✗"
        exit 1
    fi
}

# 显示用法
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "用法: $0 [full]"
    echo ""
    echo "选项:"
    echo "  full  - 运行完整测试（包括创建测试表）"
    echo ""
    echo "默认只运行基础测试（配置和连接）"
    exit 0
fi

main "$@" 