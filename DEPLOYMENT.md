# 数据库归档管理平台部署指南

## 系统要求

- Python 3.8+
- Windows/Linux/macOS
- 至少 2GB 内存
- 至少 1GB 磁盘空间

## 快速部署

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd archive_platform

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate
```

### 2. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 如果需要Oracle支持，安装oracledb
pip install oracledb
```

### 3. 配置系统

编辑 `config/archive_config.yaml` 文件：

```yaml
app:
  host: "0.0.0.0"
  port: 8080
  debug: false  # 生产环境设为false
  secret_key: "your-production-secret-key"

database:
  url: "sqlite:///data/archive_platform.db"

security:
  encryption_key: "your-production-encryption-key"
  password_salt: "your-production-password-salt"

notification:
  email:
    enabled: true
    smtp_server: "your-smtp-server"
    smtp_port: 587
    username: "your-email"
    password: "your-password"
    from_address: "your-email"
```

### 4. 启动应用

```bash
# 开发模式
python run.py

# 生产模式（推荐使用gunicorn）
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8080 "app:create_app()"
```

## 生产环境部署

### 使用Docker部署

1. 创建Dockerfile：

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs temp backups

# 暴露端口
EXPOSE 8080

# 启动应用
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:8080", "app:create_app()"]
```

2. 构建和运行：

```bash
# 构建镜像
docker build -t archive-platform .

# 运行容器
docker run -d \
  --name archive-platform \
  -p 8080:8080 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/config:/app/config \
  archive-platform
```

### 使用Nginx反向代理

创建Nginx配置文件：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 系统服务配置

创建systemd服务文件 `/etc/systemd/system/archive-platform.service`：

```ini
[Unit]
Description=Archive Platform
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/archive_platform
Environment=PATH=/path/to/archive_platform/venv/bin
ExecStart=/path/to/archive_platform/venv/bin/gunicorn -w 4 -b 127.0.0.1:8080 "app:create_app()"
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable archive-platform
sudo systemctl start archive-platform
```

## Oracle数据库配置

### 安装Oracle客户端

1. **Windows:**
   - 下载Oracle Instant Client
   - 解压到指定目录
   - 设置环境变量 `ORACLE_HOME` 和 `PATH`

2. **Linux:**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install libaio1
   
   # 下载并安装Oracle Instant Client
   wget https://download.oracle.com/otn_software/linux/instantclient/...
   sudo dpkg -i oracle-instantclient-basic_*.deb
   ```

3. **安装Python驱动:**
   ```bash
   pip install oracledb
   ```

### 配置数据库连接

在Web界面中添加Oracle数据库连接：

1. 访问 http://your-server:8080
2. 点击"数据库连接管理"
3. 添加新连接：
   - 名称：生产Oracle
   - 类型：Oracle
   - 主机：oracle-server.com
   - 端口：1521
   - 数据库：ORCL
   - 用户名：archive_user
   - 密码：your_password

## 安全配置

### 1. 更改默认密钥

```yaml
security:
  encryption_key: "生成新的32字符密钥"
  password_salt: "生成新的盐值"
```

### 2. 防火墙配置

```bash
# 只允许特定IP访问
sudo ufw allow from ***********/24 to any port 8080
```

### 3. HTTPS配置

使用Let's Encrypt获取SSL证书：

```bash
sudo certbot --nginx -d your-domain.com
```

## 监控和维护

### 日志管理

日志文件位置：
- 应用日志：`logs/app.log`
- 系统日志：数据库中的 `system_logs` 表

### 数据库备份

```bash
# 备份SQLite数据库
cp data/archive_platform.db backups/archive_platform_$(date +%Y%m%d).db

# 设置定时备份
echo "0 2 * * * cp /path/to/data/archive_platform.db /path/to/backups/archive_platform_\$(date +\%Y\%m\%d).db" | crontab -
```

### 性能监控

推荐使用以下工具：
- Prometheus + Grafana
- ELK Stack (Elasticsearch, Logstash, Kibana)
- 简单的系统监控脚本

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   netstat -tulpn | grep 8080
   # 或
   lsof -i :8080
   ```

2. **数据库连接失败**
   - 检查Oracle客户端安装
   - 验证网络连接
   - 确认数据库凭据

3. **权限问题**
   ```bash
   # 设置正确的文件权限
   chmod -R 755 /path/to/archive_platform
   chown -R www-data:www-data /path/to/archive_platform
   ```

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看系统服务日志
sudo journalctl -u archive-platform -f

# 查看Docker容器日志
docker logs -f archive-platform
```

## 升级指南

1. 备份数据和配置
2. 停止服务
3. 更新代码
4. 安装新依赖
5. 运行数据库迁移（如有）
6. 重启服务
7. 验证功能

```bash
# 升级步骤示例
sudo systemctl stop archive-platform
git pull origin main
pip install -r requirements.txt
sudo systemctl start archive-platform
```

## 联系支持

如遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 参考故障排除指南
4. 联系技术支持
