# OpenList STRM下载工具配置文件

# OpenList服务器配置
openlist:
  # OpenList服务器地址
  base_url: "http://192.168.1.7:5244"

  # 认证方式2：直接使用token（推荐）
  token: "alist-ad95f927-0cc9-4b8c-9a24-87f62ac29d5d0hKfv46OTHvRKQyxUtPcP6y33xlLRNPpFsI2VVMkuplCHum5RtkRGev7kst82Cc5"

  # 目录映射（每个源目录对应不同的本地目录）
  source_paths:
    "/123/EMBY/anime": "./downloads/anime"
    "/123/EMBY/Hentai": "./downloads/Hentai"

  # 目录密码（如果目录有密码保护）
  password: ""

# 本地存储配置
local:
  # 本地基础路径（STRM文件将保存到这里）
  base_path: "./downloads"

# STRM文件配置
strm:
  # URL格式设置
  url_format: "relative"

  # 自定义URL前缀（当url_format为"custom"时使用）
  custom_prefix: ""

  # 是否对URL进行编码
  url_encode: false

# 下载配置
max_depth: 10
concurrent_downloads: 5
download_metadata: false # 暂时禁用元数据下载以避免文件名问题
skip_error_dirs: false # 跳过有问题的目录，继续处理其他目录

# 运行模式配置
sync:
  mode: "incremental"
  cleanup_invalid: true
  confirm_mode: false # 禁用确认模式

# 高级配置
advanced:
  timeout: 60 # 增加超时时间到60秒
  retry_count: 3
  retry_delay: 5
  verify_ssl: false

# 过滤器配置
filter:
  video_extensions: []
  exclude_patterns: []
  min_file_size: 0
  max_file_size: 0
