from app import db
from datetime import datetime
import json

class ArchiveTask(db.Model):
    """归档任务配置模型"""
    __tablename__ = 'archive_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='任务名称')
    description = db.Column(db.Text, comment='任务描述')
    database_id = db.Column(db.Integer, db.ForeignKey('database_connections.id'), nullable=False, comment='数据库连接ID')
    
    # 归档配置
    table_name = db.Column(db.String(100), nullable=False, comment='表名')
    date_field = db.Column(db.String(100), nullable=False, comment='日期字段名')
    archive_condition = db.Column(db.Text, comment='归档条件SQL')
    custom_where = db.Column(db.Text, comment='自定义WHERE条件')
    
    # 文件配置
    compression_enabled = db.Column(db.Boolean, default=True, comment='是否启用压缩')
    compression_level = db.Column(db.Integer, default=6, comment='压缩级别(1-9)')
    file_prefix = db.Column(db.String(50), comment='文件名前缀')
    
    # 调度配置
    schedule_cron = db.Column(db.String(100), comment='Cron表达式')
    schedule_enabled = db.Column(db.Boolean, default=False, comment='是否启用定时调度')
    
    # 删除配置
    delete_after_archive = db.Column(db.Boolean, default=True, comment='归档后是否删除原数据')
    batch_delete_size = db.Column(db.Integer, default=5000, comment='批量删除大小')
    
    # 状态
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    last_run_time = db.Column(db.DateTime, comment='最后运行时间')
    next_run_time = db.Column(db.DateTime, comment='下次运行时间')
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关联的执行记录
    executions = db.relationship('TaskExecution', backref='archive_task', lazy=True, cascade='all, delete-orphan')
    
    # 关联的上传配置
    upload_configs = db.relationship('UploadConfig', backref='archive_task', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<ArchiveTask {self.name}>'
    
    def get_archive_condition_sql(self, archive_date):
        """生成归档条件SQL"""
        if self.custom_where:
            # 使用自定义WHERE条件
            condition = self.custom_where
        else:
            # 使用默认的日期条件
            condition = f"{self.date_field} < TO_DATE('{archive_date}', 'YYYY-MM-DD')"
        
        return f"WHERE {condition}"
    
    def get_delete_condition_sql(self, archive_date):
        """生成删除条件SQL"""
        return self.get_archive_condition_sql(archive_date)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'database_id': self.database_id,
            'database_name': self.database_connection.name if self.database_connection else None,
            'table_name': self.table_name,
            'date_field': self.date_field,
            'archive_condition': self.archive_condition,
            'custom_where': self.custom_where,
            'compression_enabled': self.compression_enabled,
            'compression_level': self.compression_level,
            'file_prefix': self.file_prefix,
            'schedule_cron': self.schedule_cron,
            'schedule_enabled': self.schedule_enabled,
            'delete_after_archive': self.delete_after_archive,
            'batch_delete_size': self.batch_delete_size,
            'is_active': self.is_active,
            'last_run_time': self.last_run_time.isoformat() if self.last_run_time else None,
            'next_run_time': self.next_run_time.isoformat() if self.next_run_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
