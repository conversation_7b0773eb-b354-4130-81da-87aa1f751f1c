#!/usr/bin/env python3
"""
OpenList连接测试工具
用于测试与OpenList服务器的连接是否正常
"""

import asyncio
import yaml
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from strm import OpenListClient, load_config


async def test_connection(config_path: str):
    """测试OpenList连接"""
    print("=" * 60)
    print("OpenList 连接测试")
    print("=" * 60)
    
    try:
        # 加载配置
        config = load_config(config_path)
        print(f"✓ 配置文件加载成功: {config_path}")
        
        # 显示配置信息
        base_url = config['openlist']['base_url']
        username = config['openlist'].get('username', '')
        source_path = config['openlist']['source_path']
        
        print(f"\n服务器地址: {base_url}")
        print(f"用户名: {username if username else '(匿名访问)'}")
        print(f"测试路径: {source_path}")
        
        # 创建客户端并测试
        print("\n开始测试连接...")
        
        async with OpenListClient(config) as client:
            # 测试登录
            if username:
                print("✓ 登录成功")
            else:
                print("✓ 匿名访问模式")
                
            # 测试列出目录
            print(f"\n尝试访问目录: {source_path}")
            dir_data = await client.list_dir(
                source_path, 
                config['openlist'].get('password', '')
            )
            
            if dir_data:
                content = dir_data.get('content', [])
                total = len(content)
                
                print(f"✓ 目录访问成功")
                print(f"✓ 找到 {total} 个项目")
                
                # 显示前10个项目
                if content:
                    print("\n前10个项目:")
                    for i, item in enumerate(content[:10]):
                        item_type = "📁" if item.get('is_dir') else "📄"
                        item_name = item.get('name', '')
                        item_size = item.get('size', 0)
                        
                        if item.get('is_dir'):
                            print(f"  {item_type} {item_name}/")
                        else:
                            size_mb = item_size / 1024 / 1024
                            print(f"  {item_type} {item_name} ({size_mb:.2f} MB)")
                            
                    if total > 10:
                        print(f"  ... 还有 {total - 10} 个项目")
                        
                print("\n✓ 连接测试成功！")
                print("您可以开始使用 strm.py 下载STRM文件了。")
            else:
                print("✗ 无法访问目录，请检查路径是否正确")
                
    except FileNotFoundError:
        print(f"✗ 配置文件不存在: {config_path}")
    except KeyError as e:
        print(f"✗ 配置文件缺少必要字段: {e}")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        
    print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OpenList连接测试工具')
    parser.add_argument(
        '-c', '--config',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    args = parser.parse_args()
    
    # 运行测试
    asyncio.run(test_connection(args.config))


if __name__ == '__main__':
    main() 