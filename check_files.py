#!/usr/bin/env python3
"""
检查并清理损坏的下载文件工具
"""

import os
import sys
from pathlib import Path
import argparse


def check_files(directory: str, min_size: int = 1024, remove_invalid: bool = False):
    """检查文件并找出可能损坏的文件"""
    
    directory = Path(directory)
    if not directory.exists():
        print(f"目录不存在: {directory}")
        return
    
    # 图片和字幕文件扩展名
    media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', 
                       '.srt', '.ass', '.ssa', '.vtt', '.sub', '.nfo'}
    
    print(f"检查目录: {directory}")
    print(f"最小文件大小: {min_size} 字节")
    print("=" * 60)
    
    total_files = 0
    small_files = 0
    html_files = 0
    removed_files = 0
    
    for file_path in directory.rglob("*"):
        if not file_path.is_file():
            continue
            
        if file_path.suffix.lower() not in media_extensions:
            continue
            
        total_files += 1
        file_size = file_path.stat().st_size
        
        is_invalid = False
        reason = ""
        
        # 检查文件大小
        if file_size < min_size:
            small_files += 1
            is_invalid = True
            reason = f"文件太小: {file_size} 字节"
        
        # 检查是否是HTML文件
        try:
            with open(file_path, 'rb') as f:
                header = f.read(50)
                if header.startswith(b'<!DOCTYPE') or header.startswith(b'<html'):
                    html_files += 1
                    is_invalid = True
                    reason = "HTML错误页面"
        except Exception as e:
            print(f"无法读取文件 {file_path}: {e}")
            continue
        
        if is_invalid:
            print(f"❌ {file_path.relative_to(directory)} - {reason}")
            
            if remove_invalid:
                try:
                    file_path.unlink()
                    removed_files += 1
                    print(f"   已删除")
                except Exception as e:
                    print(f"   删除失败: {e}")
        else:
            print(f"✅ {file_path.relative_to(directory)} - {file_size} 字节")
    
    print("=" * 60)
    print(f"检查完成:")
    print(f"  总文件数: {total_files}")
    print(f"  小文件数: {small_files}")
    print(f"  HTML文件数: {html_files}")
    if remove_invalid:
        print(f"  已删除: {removed_files}")


def main():
    parser = argparse.ArgumentParser(description="检查并清理损坏的下载文件")
    parser.add_argument("directory", help="要检查的目录路径")
    parser.add_argument("--min-size", type=int, default=1024, 
                       help="最小文件大小（字节），默认1024")
    parser.add_argument("--remove", action="store_true", 
                       help="删除检测到的无效文件")
    
    args = parser.parse_args()
    
    if args.remove:
        confirm = input(f"确定要删除 {args.directory} 中的无效文件吗? (y/N): ")
        if confirm.lower() != 'y':
            print("已取消")
            return
    
    check_files(args.directory, args.min_size, args.remove)


if __name__ == "__main__":
    main() 