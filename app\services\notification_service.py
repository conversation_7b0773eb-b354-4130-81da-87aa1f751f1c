import smtplib
import requests
import time
import hmac
import hashlib
import base64
import urllib.parse
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from flask import current_app
from app.models import NotificationConfig, SystemLog

class NotificationService:
    """通知服务类"""
    
    def __init__(self):
        self.config = current_app.config.get('ARCHIVE_CONFIG', {})
        self.notification_config = self.config.get('notification', {})
    
    def send_notification(self, notification_type, title, message, level='info'):
        """发送通知"""
        try:
            # 获取活跃的通知配置
            configs = NotificationConfig.query.filter_by(
                type=notification_type,
                is_active=True
            ).all()
            
            if not configs:
                SystemLog.log_warning('notification_service', f'没有找到活跃的{notification_type}通知配置')
                return False
            
            success_count = 0
            for config in configs:
                # 检查通知规则
                if not self._should_notify(config, level):
                    continue
                
                try:
                    if notification_type == 'email':
                        success = self._send_email(config, title, message)
                    elif notification_type == 'wechat':
                        success = self._send_wechat(config, title, message)
                    elif notification_type == 'dingtalk':
                        success = self._send_dingtalk(config, title, message)
                    else:
                        SystemLog.log_error('notification_service', f'不支持的通知类型: {notification_type}')
                        continue
                    
                    if success:
                        success_count += 1
                        SystemLog.log_info('notification_service', f'{notification_type}通知发送成功: {config.name}')
                    else:
                        SystemLog.log_error('notification_service', f'{notification_type}通知发送失败: {config.name}')
                        
                except Exception as e:
                    SystemLog.log_error('notification_service', f'{notification_type}通知发送异常: {config.name} - {str(e)}')
            
            return success_count > 0
            
        except Exception as e:
            SystemLog.log_error('notification_service', f'发送通知失败: {str(e)}')
            return False
    
    def _should_notify(self, config, level):
        """检查是否应该发送通知"""
        if level == 'success' and not config.notify_on_success:
            return False
        if level == 'error' and not config.notify_on_failure:
            return False
        if level == 'warning' and not config.notify_on_warning:
            return False
        return True
    
    def _send_email(self, config, title, message):
        """发送邮件通知"""
        try:
            email_config = config.get_config()
            
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = email_config.get('from_address')
            msg['To'] = email_config.get('to_address', email_config.get('from_address'))
            msg['Subject'] = f"[数据库归档平台] {title}"
            
            # 创建HTML邮件内容
            html_content = f"""
            <html>
            <head></head>
            <body>
                <h2>{title}</h2>
                <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
                    <pre>{message}</pre>
                </div>
                <hr>
                <p><small>此邮件由数据库归档管理平台自动发送</small></p>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(html_content, 'html', 'utf-8'))
            
            # 连接SMTP服务器
            if email_config.get('use_tls', True):
                server = smtplib.SMTP(email_config['smtp_server'], email_config.get('smtp_port', 587))
                server.starttls()
            else:
                server = smtplib.SMTP(email_config['smtp_server'], email_config.get('smtp_port', 25))
            
            # 登录并发送邮件
            if email_config.get('username') and email_config.get('password'):
                server.login(email_config['username'], email_config['password'])
            
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            SystemLog.log_error('notification_service', f'邮件发送失败: {str(e)}')
            return False
    
    def _send_wechat(self, config, title, message):
        """发送企业微信通知"""
        try:
            wechat_config = config.get_config()
            webhook_url = wechat_config.get('webhook_url')
            
            if not webhook_url:
                return False
            
            # 构建消息内容
            content = f"{title}\n\n{message}"
            
            data = {
                "msgtype": "text",
                "text": {
                    "content": content,
                    "mentioned_list": wechat_config.get('mentioned_list', [])
                }
            }
            
            response = requests.post(webhook_url, json=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            return result.get('errcode') == 0
            
        except Exception as e:
            SystemLog.log_error('notification_service', f'企业微信通知发送失败: {str(e)}')
            return False
    
    def _send_dingtalk(self, config, title, message):
        """发送钉钉通知"""
        try:
            dingtalk_config = config.get_config()
            webhook_url = dingtalk_config.get('webhook_url')
            secret = dingtalk_config.get('secret')
            
            if not webhook_url:
                return False
            
            # 如果有密钥，生成签名
            if secret:
                timestamp = str(round(time.time() * 1000))
                secret_enc = secret.encode('utf-8')
                string_to_sign = f'{timestamp}\n{secret}'
                string_to_sign_enc = string_to_sign.encode('utf-8')
                hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
                webhook_url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
            
            # 构建消息内容
            content = f"{title}\n\n{message}"
            
            data = {
                "msgtype": "text",
                "text": {
                    "content": content
                },
                "at": {
                    "atMobiles": dingtalk_config.get('at_mobiles', []),
                    "isAtAll": dingtalk_config.get('is_at_all', False)
                }
            }
            
            response = requests.post(webhook_url, json=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            return result.get('errcode') == 0
            
        except Exception as e:
            SystemLog.log_error('notification_service', f'钉钉通知发送失败: {str(e)}')
            return False
    
    def send_archive_success_notification(self, task_name, execution_id, records_exported, records_deleted, duration):
        """发送归档成功通知"""
        title = f"归档任务执行成功 - {task_name}"
        message = f"""
任务名称: {task_name}
执行ID: {execution_id}
导出记录数: {records_exported}
删除记录数: {records_deleted}
执行时长: {duration}
执行时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        # 发送各种类型的通知
        self.send_notification('email', title, message, 'success')
        self.send_notification('wechat', title, message, 'success')
        self.send_notification('dingtalk', title, message, 'success')
    
    def send_archive_failure_notification(self, task_name, execution_id, error_message):
        """发送归档失败通知"""
        title = f"归档任务执行失败 - {task_name}"
        message = f"""
任务名称: {task_name}
执行ID: {execution_id}
错误信息: {error_message}
执行时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        # 发送各种类型的通知
        self.send_notification('email', title, message, 'error')
        self.send_notification('wechat', title, message, 'error')
        self.send_notification('dingtalk', title, message, 'error')
    
    def send_test_notification(self, notification_type, config_id):
        """发送测试通知"""
        config = NotificationConfig.query.get(config_id)
        if not config:
            return False
        
        title = "数据库归档平台测试通知"
        message = f"""
这是一条来自数据库归档管理平台的测试通知。

通知配置: {config.name}
通知类型: {notification_type}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

如果您收到此消息，说明通知配置正常工作。
        """
        
        if notification_type == 'email':
            return self._send_email(config, title, message)
        elif notification_type == 'wechat':
            return self._send_wechat(config, title, message)
        elif notification_type == 'dingtalk':
            return self._send_dingtalk(config, title, message)
        
        return False
