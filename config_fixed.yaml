# OpenList STRM下载工具配置文件

# OpenList服务器配置
openlist:
  # OpenList服务器地址
  base_url: "http://192.168.1.7:5244"
  
  # 认证方式1：使用用户名密码登录
  # 留空则匿名访问
  username: "admin"
  password: "507877550@lihao"
  
  # 认证方式2：直接使用token（推荐）
  # 如果设置了token，将优先使用token认证，忽略用户名密码
  # 获取方式：访问 https://docs.openlist.team/zh/guide/api/auth/ 
  token: "alist-ad95f927-0cc9-4b8c-9a24-87f62ac29d5d0hKfv46OTHvRKQyxUtPcP6y33xlLRNPpFsI2VVMkuplCHum5RtkRGev7kst82Cc5"
  
  # 源目录路径（在OpenList中的路径）
  source_path: "/123/EMBY/anime"
  
  # 目录密码（如果目录有密码保护）
  password: ""

# 本地存储配置
local:
  # 本地基础路径（STRM文件将保存到这里）
  base_path: "./downloads"

# STRM文件配置
strm:
  # URL格式设置
  # "full": 使用完整URL (默认)
  # "relative": 只保存相对路径
  # "custom": 使用自定义前缀
  url_format: "relative"
  
  # 自定义URL前缀（当url_format为"custom"时使用）
  # 例如: "http://192.168.1.7:5244"
  custom_prefix: ""
  
  # 是否对URL进行编码
  url_encode: false

# 下载配置
# 最大文件夹深度（防止递归过深）
max_depth: 10

# 并发下载数（同时下载的文件数）
concurrent_downloads: 5

# 是否下载元数据（NFO文件和图片）
download_metadata: true

# 高级配置
advanced:
  # 请求超时时间（秒）
  timeout: 30
  
  # 重试次数
  retry_count: 3
  
  # 重试延迟（秒）
  retry_delay: 5
  
  # 是否验证SSL证书
  verify_ssl: false
  
  # 代理设置（可选）
  # proxy: "http://127.0.0.1:7890"
  
# 过滤器配置
filter:
  # 包含的视频格式（留空则使用默认）
  video_extensions: []
  # - ".mp4"
  # - ".mkv"
  
  # 排除的路径模式（正则表达式）
  exclude_patterns: []
  # - ".*\\.tmp$"
  # - ".*sample.*"
  
  # 最小文件大小（字节，0表示不限制）
  min_file_size: 0
  
  # 最大文件大小（字节，0表示不限制）
  max_file_size: 0 