# OpenList STRM下载工具稳定配置文件（处理大目录和网络问题）

# OpenList服务器配置
openlist:
  # OpenList服务器地址
  base_url: "http://192.168.1.7:5244"

  # 认证方式2：直接使用token（推荐）
  token: "alist-ad95f927-0cc9-4b8c-9a24-87f62ac29d5d0hKfv46OTHvRKQyxUtPcP6y33xlLRNPpFsI2VVMkuplCHum5RtkRGev7kst82Cc5"

  # 目录映射（每个源目录对应不同的本地目录）
  source_paths:
    "/123/EMBY/anime": "./downloads/anime"
    "/123/EMBY/Hentai": "./downloads/Hentai"

  # 目录密码（如果目录有密码保护）
  password: ""

# 本地存储配置
local:
  # 本地基础路径（STRM文件将保存到这里）
  base_path: "./downloads"

# STRM文件配置
strm:
  # URL格式设置
  url_format: "relative"

  # 自定义URL前缀（当url_format为"custom"时使用）
  custom_prefix: ""

  # 是否对URL进行编码
  url_encode: false

# 下载配置 - 稳定设置
max_depth: 10
concurrent_downloads: 10 # 较低并发以保证稳定性
download_metadata: true # 启用元数据下载（包括NFO、图片、字幕文件）
skip_error_dirs: true # 跳过有问题的目录（如超时目录）
request_delay: 0.2 # 增加请求间延迟以避免服务器压力

# 运行模式配置
sync:
  mode: "incremental"
  cleanup_invalid: true
  confirm_mode: false

# 高级配置 - 稳定设置
advanced:
  timeout: 60 # 一般请求超时时间（增加到1分钟）
  dir_timeout: 300 # 目录列表超时时间（5分钟，处理超大目录）
  retry_count: 3 # 增加重试次数
  retry_delay: 5 # 增加重试延迟
  verify_ssl: false

  # 性能监控设置
  show_progress: true
  progress_interval: 1000 # 每10个文件显示一次进度
  show_network_stats: true # 显示网络统计信息

# 过滤器配置
filter:
  video_extensions: []
  exclude_patterns: []
  min_file_size: 0
  max_file_size: 0

# 日志配置
logging:
  level: "INFO" # INFO级别，减少日志量
  show_timing: true
  show_memory_usage: false
