{% extends "base.html" %}

{% block title %}归档任务管理 - 数据库归档管理平台{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">归档任务管理</li>
{% endblock %}

{% block page_title %}归档任务管理{% endblock %}

{% block page_actions %}
<button class="btn btn-primary btn-icon" onclick="showTaskDialog()">
    <i class="fas fa-plus"></i>
    <span>创建任务</span>
</button>
{% endblock %}

{% block content %}
<!-- 任务统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-tasks fa-2x text-primary mb-2"></i>
                <h4 class="mb-0" id="total-tasks">0</h4>
                <small class="text-muted">总任务数</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-play-circle fa-2x text-success mb-2"></i>
                <h4 class="mb-0" id="active-tasks">0</h4>
                <small class="text-muted">活跃任务</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4 class="mb-0" id="scheduled-tasks">0</h4>
                <small class="text-muted">定时任务</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                <h4 class="mb-0" id="completed-tasks">0</h4>
                <small class="text-muted">已完成</small>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>任务列表
        </h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-secondary" onclick="refreshTasks()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="btn btn-sm btn-outline-success" onclick="runAllTasks()">
                <i class="fas fa-play"></i> 执行全部
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>数据库连接</th>
                        <th>表名</th>
                        <th>状态</th>
                        <th>调度</th>
                        <th>最后执行</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tasks-table-body">
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载任务列表...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 任务配置模态框 -->
<div class="modal fade" id="taskModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskModalTitle">
                    <i class="fas fa-plus me-2"></i>创建归档任务
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="taskForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="task_name" class="form-label">任务名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="task_name" name="task_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="connection_id" class="form-label">数据库连接 <span class="text-danger">*</span></label>
                                <select class="form-select" id="connection_id" name="connection_id" required>
                                    <option value="">请选择数据库连接</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="table_name" class="form-label">表名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="table_name" name="table_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_column" class="form-label">日期字段 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="date_column" name="date_column" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="archive_condition" class="form-label">归档条件</label>
                        <textarea class="form-control" id="archive_condition" name="archive_condition" rows="3" 
                                  placeholder="例如: created_date < SYSDATE - 365"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="retention_days" class="form-label">保留天数</label>
                                <input type="number" class="form-control" id="retention_days" name="retention_days" value="365">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batch_size" class="form-label">批处理大小</label>
                                <input type="number" class="form-control" id="batch_size" name="batch_size" value="1000">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="schedule_enabled" name="schedule_enabled">
                                    <label class="form-check-label" for="schedule_enabled">
                                        启用定时执行
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="schedule_cron" class="form-label">Cron表达式</label>
                                <input type="text" class="form-control" id="schedule_cron" name="schedule_cron" 
                                       placeholder="0 2 * * *">
                                <div class="form-text">例如: 0 2 * * * (每天凌晨2点执行)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                启用此任务
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" onclick="validateTask()">
                    <i class="fas fa-check"></i> 验证配置
                </button>
                <button type="button" class="btn btn-primary" onclick="saveTask()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTasks();
    loadConnections();
    updateTaskStats();
});

// 显示任务配置对话框
function showTaskDialog(task = null) {
    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
    const form = document.getElementById('taskForm');
    const title = document.getElementById('taskModalTitle');
    
    if (task) {
        // 编辑模式
        title.innerHTML = '<i class="fas fa-edit me-2"></i>编辑归档任务';
        // 填充表单数据
        Object.keys(task).forEach(key => {
            const field = form.elements[key];
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = task[key];
                } else {
                    field.value = task[key];
                }
            }
        });
        form.dataset.taskId = task.id;
    } else {
        // 新增模式
        title.innerHTML = '<i class="fas fa-plus me-2"></i>创建归档任务';
        form.reset();
        form.is_active.checked = true;
        delete form.dataset.taskId;
    }
    
    modal.show();
}

// 加载任务列表
async function loadTasks() {
    try {
        const response = await axios.get('/api/archive-tasks');
        const tasks = response.data.data;
        
        renderTasksTable(tasks);
        updateTaskStats();
    } catch (error) {
        console.error('加载任务列表失败:', error);
        showMessage('加载任务列表失败: ' + error.message, 'error');
    }
}

// 渲染任务表格
function renderTasksTable(tasks) {
    const tbody = document.getElementById('tasks-table-body');
    
    if (tasks.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无归档任务</div>
                    <button class="btn btn-primary btn-sm mt-2" onclick="showTaskDialog()">
                        <i class="fas fa-plus me-1"></i>创建第一个任务
                    </button>
                </td>
            </tr>
        `;
        return;
    }
    
    const html = tasks.map(task => `
        <tr>
            <td>
                <div class="fw-bold">${task.name}</div>
                <small class="text-muted">ID: ${task.id}</small>
            </td>
            <td>${task.connection_name || '--'}</td>
            <td>${task.table_name}</td>
            <td>
                <span class="status-badge status-${task.is_active ? 'success' : 'error'}">
                    <i class="fas fa-${task.is_active ? 'check' : 'times'}-circle"></i>
                    ${task.is_active ? '启用' : '禁用'}
                </span>
            </td>
            <td>
                ${task.schedule_enabled ? 
                    `<span class="badge bg-info">${task.schedule_cron || '--'}</span>` : 
                    '<span class="text-muted">手动执行</span>'
                }
            </td>
            <td>${formatDateTime(task.last_run_time)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-success" onclick="runTask(${task.id})" title="立即执行">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewTaskHistory(${task.id})" title="执行历史">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn btn-outline-primary" onclick="editTask(${task.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteTask(${task.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// 加载数据库连接列表
async function loadConnections() {
    try {
        const response = await axios.get('/api/database-connections');
        const connections = response.data.data;
        
        const select = document.getElementById('connection_id');
        select.innerHTML = '<option value="">请选择数据库连接</option>';
        
        connections.forEach(conn => {
            if (conn.is_active) {
                const option = document.createElement('option');
                option.value = conn.id;
                option.textContent = `${conn.name} (${conn.host})`;
                select.appendChild(option);
            }
        });
    } catch (error) {
        console.error('加载数据库连接失败:', error);
    }
}

// 更新任务统计
async function updateTaskStats() {
    try {
        const response = await axios.get('/api/archive-tasks');
        const tasks = response.data.data;
        
        const total = tasks.length;
        const active = tasks.filter(t => t.is_active).length;
        const scheduled = tasks.filter(t => t.schedule_enabled).length;
        const completed = tasks.filter(t => t.last_run_time).length;
        
        document.getElementById('total-tasks').textContent = total;
        document.getElementById('active-tasks').textContent = active;
        document.getElementById('scheduled-tasks').textContent = scheduled;
        document.getElementById('completed-tasks').textContent = completed;
    } catch (error) {
        console.error('更新任务统计失败:', error);
    }
}

// 保存任务
async function saveTask() {
    const form = document.getElementById('taskForm');
    
    if (!validateForm(form)) {
        showMessage('请填写所有必填字段', 'error');
        return;
    }
    
    const formData = new FormData(form);
    const data = {
        name: formData.get('task_name'),
        connection_id: parseInt(formData.get('connection_id')),
        table_name: formData.get('table_name'),
        date_column: formData.get('date_column'),
        archive_condition: formData.get('archive_condition'),
        retention_days: parseInt(formData.get('retention_days')) || 365,
        batch_size: parseInt(formData.get('batch_size')) || 1000,
        schedule_enabled: formData.has('schedule_enabled'),
        schedule_cron: formData.get('schedule_cron'),
        is_active: formData.has('is_active')
    };

    try {
        showLoading();
        let response;
        
        if (form.dataset.taskId) {
            // 更新任务
            response = await axios.put(`/api/archive-tasks/${form.dataset.taskId}`, data);
        } else {
            // 创建任务
            response = await axios.post('/api/archive-tasks', data);
        }

        if (response.data.success) {
            showMessage(response.data.message, 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('taskModal'));
            modal.hide();
            
            // 重新加载任务列表
            loadTasks();
        } else {
            showMessage(response.data.message, 'error');
        }
    } catch (error) {
        console.error('保存任务失败:', error);
        showMessage('保存任务失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 刷新任务列表
function refreshTasks() {
    loadTasks();
}

// 运行任务
async function runTask(id) {
    const confirmed = await confirmDialog('确定要立即执行这个归档任务吗？');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.post(`/api/archive-tasks/${id}/execute`);
        
        if (response.data.success) {
            showMessage('任务已开始执行', 'success');
            loadTasks();
        } else {
            showMessage('任务执行失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('执行任务失败:', error);
        showMessage('执行任务失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 编辑任务
async function editTask(id) {
    try {
        const response = await axios.get(`/api/archive-tasks/${id}`);
        const task = response.data.data;
        
        showTaskDialog(task);
    } catch (error) {
        console.error('获取任务信息失败:', error);
        showMessage('获取任务信息失败: ' + error.message, 'error');
    }
}

// 删除任务
async function deleteTask(id) {
    const confirmed = await confirmDialog('确定要删除这个归档任务吗？此操作不可撤销。');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.delete(`/api/archive-tasks/${id}`);
        
        if (response.data.success) {
            showMessage(response.data.message, 'success');
            loadTasks();
        } else {
            showMessage(response.data.message, 'error');
        }
    } catch (error) {
        console.error('删除任务失败:', error);
        showMessage('删除任务失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 查看任务执行历史
function viewTaskHistory(id) {
    window.location.href = `/execution-history?task_id=${id}`;
}

// 验证任务配置
async function validateTask() {
    const form = document.getElementById('taskForm');
    
    if (!validateForm(form)) {
        showMessage('请填写所有必填字段', 'error');
        return;
    }
    
    // 这里可以添加更多的验证逻辑
    showMessage('任务配置验证通过', 'success');
}

// 运行所有任务
async function runAllTasks() {
    const confirmed = await confirmDialog('确定要执行所有活跃的归档任务吗？');
    if (!confirmed) return;

    try {
        showLoading();
        const response = await axios.post('/api/archive-tasks/execute-all');
        
        if (response.data.success) {
            showMessage('所有任务已开始执行', 'success');
            loadTasks();
        } else {
            showMessage('批量执行失败: ' + response.data.message, 'error');
        }
    } catch (error) {
        console.error('批量执行失败:', error);
        showMessage('批量执行失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}
</script>
{% endblock %}
