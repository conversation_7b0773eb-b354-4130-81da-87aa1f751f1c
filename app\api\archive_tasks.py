from flask import request, jsonify, current_app
from app import db
from app.models import ArchiveTask, DatabaseConnection, SystemLog
from app.api import api_bp
from datetime import datetime

@api_bp.route('/archive-tasks', methods=['GET'])
def get_archive_tasks():
    """获取归档任务列表"""
    try:
        tasks = ArchiveTask.query.all()
        return jsonify({
            'success': True,
            'data': [task.to_dict() for task in tasks]
        })
    except Exception as e:
        SystemLog.log_error('archive_tasks', f'获取归档任务列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取归档任务列表失败: {str(e)}'
        }), 500

@api_bp.route('/archive-tasks', methods=['POST'])
def create_archive_task():
    """创建归档任务"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'database_id', 'table_name', 'date_field']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查名称是否已存在
        existing = ArchiveTask.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({
                'success': False,
                'message': '任务名称已存在'
            }), 400
        
        # 检查数据库连接是否存在
        database_conn = DatabaseConnection.query.get(data['database_id'])
        if not database_conn:
            return jsonify({
                'success': False,
                'message': '指定的数据库连接不存在'
            }), 400
        
        # 创建新任务
        task = ArchiveTask(
            name=data['name'],
            description=data.get('description', ''),
            database_id=data['database_id'],
            table_name=data['table_name'],
            date_field=data['date_field'],
            archive_condition=data.get('archive_condition'),
            custom_where=data.get('custom_where'),
            compression_enabled=data.get('compression_enabled', True),
            compression_level=data.get('compression_level', 6),
            file_prefix=data.get('file_prefix'),
            schedule_cron=data.get('schedule_cron'),
            schedule_enabled=data.get('schedule_enabled', False),
            delete_after_archive=data.get('delete_after_archive', True),
            batch_delete_size=data.get('batch_delete_size', 5000),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(task)
        db.session.commit()
        
        SystemLog.log_info('archive_tasks', f'创建归档任务: {task.name}')
        
        return jsonify({
            'success': True,
            'message': '归档任务创建成功',
            'data': task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('archive_tasks', f'创建归档任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'创建归档任务失败: {str(e)}'
        }), 500

@api_bp.route('/archive-tasks/<int:task_id>', methods=['PUT'])
def update_archive_task(task_id):
    """更新归档任务"""
    try:
        task = ArchiveTask.query.get_or_404(task_id)
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            # 检查名称是否已被其他任务使用
            existing = ArchiveTask.query.filter(
                ArchiveTask.name == data['name'],
                ArchiveTask.id != task_id
            ).first()
            if existing:
                return jsonify({
                    'success': False,
                    'message': '任务名称已存在'
                }), 400
            task.name = data['name']
        
        # 更新其他字段
        updatable_fields = [
            'description', 'database_id', 'table_name', 'date_field',
            'archive_condition', 'custom_where', 'compression_enabled',
            'compression_level', 'file_prefix', 'schedule_cron',
            'schedule_enabled', 'delete_after_archive', 'batch_delete_size',
            'is_active'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(task, field, data[field])
        
        db.session.commit()
        
        SystemLog.log_info('archive_tasks', f'更新归档任务: {task.name}')
        
        return jsonify({
            'success': True,
            'message': '归档任务更新成功',
            'data': task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('archive_tasks', f'更新归档任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'更新归档任务失败: {str(e)}'
        }), 500

@api_bp.route('/archive-tasks/<int:task_id>', methods=['DELETE'])
def delete_archive_task(task_id):
    """删除归档任务"""
    try:
        task = ArchiveTask.query.get_or_404(task_id)
        
        task_name = task.name
        db.session.delete(task)
        db.session.commit()
        
        SystemLog.log_info('archive_tasks', f'删除归档任务: {task_name}')
        
        return jsonify({
            'success': True,
            'message': '归档任务删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('archive_tasks', f'删除归档任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除归档任务失败: {str(e)}'
        }), 500

@api_bp.route('/archive-tasks/<int:task_id>/execute', methods=['POST'])
def execute_archive_task(task_id):
    """手动执行归档任务"""
    try:
        task = ArchiveTask.query.get_or_404(task_id)
        
        if not task.is_active:
            return jsonify({
                'success': False,
                'message': '任务未启用，无法执行'
            }), 400
        
        # 这里应该调用归档执行服务
        # 暂时返回成功消息
        SystemLog.log_info('archive_tasks', f'手动执行归档任务: {task.name}')
        
        return jsonify({
            'success': True,
            'message': f'归档任务 {task.name} 已开始执行'
        })
        
    except Exception as e:
        SystemLog.log_error('archive_tasks', f'执行归档任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'执行归档任务失败: {str(e)}'
        }), 500

@api_bp.route('/archive-tasks/<int:task_id>/toggle', methods=['POST'])
def toggle_archive_task(task_id):
    """启用/禁用归档任务"""
    try:
        task = ArchiveTask.query.get_or_404(task_id)
        
        task.is_active = not task.is_active
        db.session.commit()
        
        status = '启用' if task.is_active else '禁用'
        SystemLog.log_info('archive_tasks', f'{status}归档任务: {task.name}')
        
        return jsonify({
            'success': True,
            'message': f'归档任务已{status}',
            'data': task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        SystemLog.log_error('archive_tasks', f'切换归档任务状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'切换归档任务状态失败: {str(e)}'
        }), 500
