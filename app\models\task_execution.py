from app import db
from datetime import datetime
import json

class TaskExecution(db.Model):
    """任务执行记录模型"""
    __tablename__ = 'task_executions'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.<PERSON>teger, db.<PERSON>ey('archive_tasks.id'), nullable=False, comment='任务ID')
    
    # 执行信息
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    status = db.Column(db.String(20), nullable=False, default='running', comment='执行状态')  # running, success, failed, cancelled
    
    # 统计信息
    records_exported = db.Column(db.Integer, default=0, comment='导出记录数')
    records_deleted = db.Column(db.Integer, default=0, comment='删除记录数')
    file_path = db.Column(db.String(500), comment='生成的文件路径')
    file_size = db.Column(db.BigInteger, comment='文件大小(字节)')
    compressed_file_path = db.Column(db.String(500), comment='压缩文件路径')
    compressed_file_size = db.Column(db.BigInteger, comment='压缩文件大小(字节)')
    
    # 上传信息
    upload_status = db.Column(db.String(20), default='pending', comment='上传状态')  # pending, uploading, success, failed
    upload_path = db.Column(db.String(500), comment='上传路径')
    upload_error = db.Column(db.Text, comment='上传错误信息')
    
    # 错误信息
    error_message = db.Column(db.Text, comment='错误信息')
    error_details = db.Column(db.Text, comment='详细错误信息')
    
    # 执行日志
    execution_log = db.Column(db.Text, comment='执行日志')
    
    # 通知状态
    notification_sent = db.Column(db.Boolean, default=False, comment='是否已发送通知')
    notification_error = db.Column(db.Text, comment='通知发送错误')
    
    def __repr__(self):
        return f'<TaskExecution {self.id}>'
    
    @property
    def duration(self):
        """计算执行时长(秒)"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            return (datetime.utcnow() - self.start_time).total_seconds()
        return 0
    
    @property
    def duration_formatted(self):
        """格式化的执行时长"""
        duration = self.duration
        if duration < 60:
            return f"{duration:.1f}秒"
        elif duration < 3600:
            return f"{duration/60:.1f}分钟"
        else:
            return f"{duration/3600:.1f}小时"
    
    def add_log(self, message):
        """添加执行日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        
        if self.execution_log:
            self.execution_log += log_entry
        else:
            self.execution_log = log_entry
    
    def set_error(self, error_message, error_details=None):
        """设置错误信息"""
        self.status = 'failed'
        self.error_message = error_message
        self.error_details = error_details
        self.end_time = datetime.utcnow()
        self.add_log(f"执行失败: {error_message}")
    
    def set_success(self):
        """设置成功状态"""
        self.status = 'success'
        self.end_time = datetime.utcnow()
        self.add_log("执行成功完成")
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'task_name': self.archive_task.name if self.archive_task else None,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'duration': self.duration,
            'duration_formatted': self.duration_formatted,
            'records_exported': self.records_exported,
            'records_deleted': self.records_deleted,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'compressed_file_path': self.compressed_file_path,
            'compressed_file_size': self.compressed_file_size,
            'upload_status': self.upload_status,
            'upload_path': self.upload_path,
            'upload_error': self.upload_error,
            'error_message': self.error_message,
            'notification_sent': self.notification_sent,
            'notification_error': self.notification_error
        }
