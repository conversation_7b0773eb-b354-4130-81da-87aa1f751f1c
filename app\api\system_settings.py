"""
系统设置API模块
"""
from flask import request, jsonify
import json
import os
from datetime import datetime
from . import api_bp

# 默认设置
DEFAULT_SETTINGS = {
    'general': {
        'app_name': '数据库归档管理平台',
        'app_version': 'v1.0.0',
        'default_batch_size': 1000,
        'default_retention_days': 365,
        'max_concurrent_tasks': 5,
        'task_timeout': 60,
        'auto_cleanup': True,
        'enable_monitoring': True,
        'auto_backup': False,
        'enable_api_rate_limit': False
    },
    'compression': {
        'compression_enabled': True,
        'compression_format': 'zip',
        'compression_level': 6,
        'min_file_size': 10,
        'compression_password': '',
        'delete_original': False,
        'verify_compression': True,
        'split_large_files': False,
        'split_size': 100
    },
    'naming': {
        'file_name_template': '{table_name}_{date}_{time}',
        'date_format': 'YYYYMMDD',
        'time_format': 'HHMMSS'
    },
    'security': {
        'min_password_length': 8,
        'password_expiry_days': 90,
        'require_special_chars': False,
        'session_timeout': 30,
        'max_login_attempts': 5,
        'allowed_ips': '',
        'encryption_algorithm': 'AES-256',
        'encrypt_database_passwords': True,
        'encrypt_export_files': False
    },
    'logs': {
        'log_level': 'INFO',
        'log_retention_days': 30,
        'max_log_file_size': 10,
        'log_backup_count': 5,
        'enable_sql_logging': False,
        'enable_performance_logging': True,
        'enable_audit_logging': True
    }
}

def get_settings_file_path():
    """获取设置文件路径"""
    return os.path.join('config', 'system_settings.json')

def load_settings():
    """加载系统设置"""
    settings_file = get_settings_file_path()
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                # 合并默认设置，确保所有字段都存在
                for category, defaults in DEFAULT_SETTINGS.items():
                    if category not in settings:
                        settings[category] = {}
                    for key, value in defaults.items():
                        if key not in settings[category]:
                            settings[category][key] = value
                return settings
        except Exception as e:
            print(f"加载设置文件失败: {e}")
            return DEFAULT_SETTINGS.copy()
    return DEFAULT_SETTINGS.copy()

def save_settings(settings):
    """保存系统设置"""
    settings_file = get_settings_file_path()
    os.makedirs(os.path.dirname(settings_file), exist_ok=True)
    
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存设置文件失败: {e}")
        return False

@api_bp.route('/system/settings', methods=['GET'])
def get_system_settings():
    """获取系统设置"""
    try:
        settings = load_settings()
        return jsonify({
            'success': True,
            'data': settings
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取系统设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/general', methods=['POST'])
def save_general_settings():
    """保存基本设置"""
    try:
        data = request.get_json()
        settings = load_settings()
        settings['general'].update(data)

        if save_settings(settings):
            return jsonify({
                'success': True,
                'message': '基本设置保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '保存设置失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存基本设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/compression', methods=['POST'])
def save_compression_settings():
    """保存压缩设置"""
    try:
        data = request.get_json()
        settings = load_settings()
        settings['compression'].update(data)

        if save_settings(settings):
            return jsonify({
                'success': True,
                'message': '压缩设置保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '保存设置失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存压缩设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/naming', methods=['POST'])
def save_naming_settings():
    """保存文件命名设置"""
    try:
        data = request.get_json()
        settings = load_settings()
        settings['naming'].update(data)

        if save_settings(settings):
            return jsonify({
                'success': True,
                'message': '文件命名设置保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '保存设置失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存文件命名设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/security', methods=['POST'])
def save_security_settings():
    """保存安全设置"""
    try:
        data = request.get_json()
        settings = load_settings()
        settings['security'].update(data)

        if save_settings(settings):
            return jsonify({
                'success': True,
                'message': '安全设置保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '保存设置失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存安全设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/logs', methods=['POST'])
def save_logs_settings():
    """保存日志设置"""
    try:
        data = request.get_json()
        settings = load_settings()
        settings['logs'].update(data)

        if save_settings(settings):
            return jsonify({
                'success': True,
                'message': '日志设置保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '保存设置失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存日志设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/test-compression', methods=['POST'])
def test_compression():
    """测试压缩功能"""
    try:
        # 这里可以实现实际的压缩测试逻辑
        return jsonify({
            'success': True,
            'message': '压缩测试成功，压缩率约65%'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'压缩测试失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/export', methods=['GET'])
def export_settings():
    """导出系统设置"""
    try:
        settings = load_settings()
        settings['export_info'] = {
            'export_time': datetime.now().isoformat(),
            'version': '1.0.0'
        }

        return jsonify({
            'success': True,
            'data': settings
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/import', methods=['POST'])
def import_settings():
    """导入系统设置"""
    try:
        data = request.get_json()

        # 验证导入的数据格式
        if not isinstance(data, dict):
            return jsonify({
                'success': False,
                'message': '无效的设置文件格式'
            }), 400

        # 移除导出信息
        if 'export_info' in data:
            del data['export_info']

        if save_settings(data):
            return jsonify({
                'success': True,
                'message': '设置导入成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '保存导入的设置失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/reset', methods=['POST'])
def reset_settings():
    """重置为默认设置"""
    try:
        if save_settings(DEFAULT_SETTINGS.copy()):
            return jsonify({
                'success': True,
                'message': '设置已重置为默认值'
            })
        else:
            return jsonify({
                'success': False,
                'message': '重置设置失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'重置设置失败: {str(e)}'
        }), 500

@api_bp.route('/system/settings/validate', methods=['POST'])
def validate_settings():
    """验证系统设置"""
    try:
        settings = load_settings()
        valid_count = 0
        invalid_count = 0
        warnings = []

        # 验证基本设置
        general = settings.get('general', {})
        if general.get('max_concurrent_tasks', 0) > 20:
            warnings.append('并发任务数过高，可能影响系统性能')
            invalid_count += 1
        else:
            valid_count += 1

        if general.get('task_timeout', 0) < 5:
            warnings.append('任务超时时间过短，可能导致任务异常终止')
            invalid_count += 1
        else:
            valid_count += 1

        # 验证压缩设置
        compression = settings.get('compression', {})
        if compression.get('min_file_size', 0) < 1:
            warnings.append('最小压缩文件大小设置过小')
            invalid_count += 1
        else:
            valid_count += 1

        return jsonify({
            'success': True,
            'data': {
                'valid_count': valid_count,
                'invalid_count': invalid_count,
                'warnings': warnings
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'验证设置失败: {str(e)}'
        }), 500
