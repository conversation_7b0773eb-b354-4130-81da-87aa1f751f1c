from app import db
from datetime import datetime
from cryptography.fernet import Fernet
import base64
import os

class DatabaseConnection(db.Model):
    """数据库连接配置模型"""
    __tablename__ = 'database_connections'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='连接名称')
    db_type = db.Column(db.String(20), nullable=False, default='oracle', comment='数据库类型')
    host = db.Column(db.String(255), nullable=False, comment='主机地址')
    port = db.Column(db.Integer, nullable=False, default=1521, comment='端口号')
    database = db.Column(db.String(100), nullable=False, comment='数据库名/服务名')
    username = db.Column(db.String(100), nullable=False, comment='用户名')
    password_encrypted = db.Column(db.Text, nullable=False, comment='加密后的密码')
    is_active = db.Column(db.<PERSON>, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关联的归档任务
    archive_tasks = db.relationship('ArchiveTask', backref='database_connection', lazy=True)
    
    def __repr__(self):
        return f'<DatabaseConnection {self.name}>'
    
    def set_password(self, password, encryption_key):
        """加密并设置密码"""
        fernet = Fernet(encryption_key.encode())
        encrypted_password = fernet.encrypt(password.encode())
        self.password_encrypted = base64.b64encode(encrypted_password).decode()
    
    def get_password(self, encryption_key):
        """解密并获取密码"""
        try:
            fernet = Fernet(encryption_key.encode())
            encrypted_password = base64.b64decode(self.password_encrypted.encode())
            return fernet.decrypt(encrypted_password).decode()
        except Exception as e:
            raise ValueError(f"密码解密失败: {str(e)}")
    
    def get_connection_string(self, encryption_key):
        """获取数据库连接字符串"""
        password = self.get_password(encryption_key)

        if self.db_type.lower() == 'oracle':
            return f"oracle+oracledb://{self.username}:{password}@{self.host}:{self.port}/{self.database}"
        else:
            raise ValueError(f"不支持的数据库类型: {self.db_type}")

    def test_connection(self, encryption_key):
        """测试数据库连接"""
        try:
            # 暂时模拟连接测试，因为oracledb需要编译环境
            # import oracledb
            password = self.get_password(encryption_key)

            # 创建连接字符串
            dsn = f"{self.host}:{self.port}/{self.database}"

            # 模拟连接测试
            if self.host and self.username and password:
                return True, "连接配置验证成功（注意：实际Oracle连接需要安装oracledb库）"
            else:
                return False, "连接配置不完整"

        except Exception as e:
            return False, f"连接测试失败: {str(e)}"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'db_type': self.db_type,
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
